/**
 * Export Dialog Component
 * Phase 6: Frontend Development - Task 6.3
 */

import React, { useState } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  FormControlLabel,
  Checkbox,
  Box,
  Typography,
  Alert,
  LinearProgress,
  Chip,
  Divider,
} from '@mui/material';
import {
  Download as DownloadIcon,
  Close as CloseIcon,
  FileDownload as FileDownloadIcon,
  TableChart as TableIcon,
  Description as JsonIcon,
  GridOn as CsvIcon,
} from '@mui/icons-material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { ExportRequest } from '../types/api';
import { useQueryHistory } from '../contexts/AppContext';
import api from '../services/api';

interface ExportDialogProps {
  open: boolean;
  onClose: () => void;
  queryResult?: any;
  exportType?: 'query' | 'history' | 'companies' | 'filings';
}

const ExportDialog: React.FC<ExportDialogProps> = ({
  open,
  onClose,
  queryResult,
  exportType = 'query',
}) => {
  const { queryHistory } = useQueryHistory();
  
  // Form state
  const [format, setFormat] = useState<'json' | 'csv' | 'xlsx'>('json');
  const [includeSources, setIncludeSources] = useState(true);
  const [includeMetadata, setIncludeMetadata] = useState(true);
  const [startDate, setStartDate] = useState<Date | null>(null);
  const [endDate, setEndDate] = useState<Date | null>(null);
  const [selectedCompanies] = useState<string[]>([]);
  const [selectedFilingTypes] = useState<string[]>([]);
  
  // UI state
  const [isExporting, setIsExporting] = useState(false);
  const [exportError, setExportError] = useState<string>('');
  const [exportSuccess, setExportSuccess] = useState(false);

  // Format options
  const formatOptions = [
    { value: 'json', label: 'JSON', icon: <JsonIcon />, description: 'Structured data format' },
    { value: 'csv', label: 'CSV', icon: <CsvIcon />, description: 'Spreadsheet compatible' },
    { value: 'xlsx', label: 'Excel', icon: <TableIcon />, description: 'Microsoft Excel format' },
  ];

  // Filing types (available for future use)
  // const filingTypes = ['10-K', '10-Q', '8-K', 'DEF 14A'];

  // Handle export
  const handleExport = async () => {
    setIsExporting(true);
    setExportError('');
    setExportSuccess(false);

    try {
      let exportData: any = null;
      let filename = '';

      switch (exportType) {
        case 'query':
          if (queryResult) {
            exportData = {
              query: queryResult,
              exported_at: new Date().toISOString(),
              format: format,
              options: {
                include_sources: includeSources,
                include_metadata: includeMetadata,
              },
            };
            filename = `query_result_${Date.now()}.${format}`;
          }
          break;

        case 'history':
          exportData = {
            query_history: queryHistory,
            total_queries: queryHistory.length,
            exported_at: new Date().toISOString(),
            date_range: {
              start: startDate?.toISOString(),
              end: endDate?.toISOString(),
            },
          };
          filename = `query_history_${Date.now()}.${format}`;
          break;

        case 'companies':
          // This would call the companies API with export functionality
          const companiesExportRequest: ExportRequest = {
            format,
            include_sources: includeSources,
            include_metadata: includeMetadata,
            companies: selectedCompanies.length > 0 ? selectedCompanies : undefined,
            date_range: startDate && endDate ? {
              start_date: startDate.toISOString().split('T')[0],
              end_date: endDate.toISOString().split('T')[0],
            } : undefined,
          };
          
          await api.export.requestExport(companiesExportRequest);
          filename = `companies_export_${Date.now()}.${format}`;
          break;

        case 'filings':
          // This would call the filings API with export functionality
          const filingsExportRequest: ExportRequest = {
            format,
            include_sources: includeSources,
            include_metadata: includeMetadata,
            companies: selectedCompanies.length > 0 ? selectedCompanies : undefined,
            filing_types: selectedFilingTypes.length > 0 ? selectedFilingTypes : undefined,
            date_range: startDate && endDate ? {
              start_date: startDate.toISOString().split('T')[0],
              end_date: endDate.toISOString().split('T')[0],
            } : undefined,
          };
          
          await api.export.requestExport(filingsExportRequest);
          filename = `filings_export_${Date.now()}.${format}`;
          break;
      }

      if (exportData) {
        // Create and download file
        const blob = createExportBlob(exportData, format);
        downloadBlob(blob, filename);
        setExportSuccess(true);
        
        // Close dialog after short delay
        setTimeout(() => {
          onClose();
          setExportSuccess(false);
        }, 2000);
      }

    } catch (error: any) {
      console.error('Export failed:', error);
      setExportError(error.message || 'Export failed. Please try again.');
    } finally {
      setIsExporting(false);
    }
  };

  // Create export blob
  const createExportBlob = (data: any, format: string): Blob => {
    switch (format) {
      case 'json':
        return new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
      
      case 'csv':
        const csv = convertToCSV(data);
        return new Blob([csv], { type: 'text/csv' });
      
      case 'xlsx':
        // For XLSX, we'd need a library like xlsx or exceljs
        // For now, fall back to CSV
        const csvData = convertToCSV(data);
        return new Blob([csvData], { type: 'text/csv' });
      
      default:
        return new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
    }
  };

  // Convert data to CSV
  const convertToCSV = (data: any): string => {
    if (exportType === 'query' && data.query) {
      const query = data.query;
      let csv = 'Field,Value\n';
      csv += `Question,"${query.question || ''}"\n`;
      csv += `Answer,"${query.answer || ''}"\n`;
      csv += `Success,${query.success}\n`;
      csv += `Processing Time,${query.processing_time || 0}\n`;
      csv += `Model Used,"${query.model_used || ''}"\n`;
      csv += `Confidence,"${query.confidence || ''}"\n`;
      
      if (query.sources && query.sources.length > 0) {
        csv += '\n\nSources\n';
        csv += 'Ticker,Filing Type,Filing Date,Section,Relevance Score\n';
        query.sources.forEach((source: any) => {
          csv += `"${source.ticker}","${source.filing_type}","${source.filing_date}","${source.section}",${source.relevance_score}\n`;
        });
      }
      
      return csv;
    }
    
    if (exportType === 'history' && data.query_history) {
      let csv = 'Query,Timestamp\n';
      data.query_history.forEach((query: string, index: number) => {
        const timestamp = new Date(Date.now() - index * 60000).toISOString();
        csv += `"${query}","${timestamp}"\n`;
      });
      return csv;
    }
    
    return JSON.stringify(data, null, 2);
  };

  // Download blob
  const downloadBlob = (blob: Blob, filename: string) => {
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  };

  // Reset form when dialog closes
  const handleClose = () => {
    setExportError('');
    setExportSuccess(false);
    onClose();
  };

  return (
    <LocalizationProvider dateAdapter={AdapterDateFns}>
      <Dialog
        open={open}
        onClose={handleClose}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>
          <Box display="flex" alignItems="center" gap={1}>
            <FileDownloadIcon />
            Export {exportType === 'query' ? 'Query Result' : 
                   exportType === 'history' ? 'Query History' :
                   exportType === 'companies' ? 'Companies Data' : 'Filings Data'}
          </Box>
        </DialogTitle>

        <DialogContent>
          {isExporting && <LinearProgress sx={{ mb: 2 }} />}
          
          {exportError && (
            <Alert severity="error" sx={{ mb: 2 }}>
              {exportError}
            </Alert>
          )}
          
          {exportSuccess && (
            <Alert severity="success" sx={{ mb: 2 }}>
              Export completed successfully! File download should start automatically.
            </Alert>
          )}

          <Box display="flex" flexDirection="column" gap={3}>
            {/* Format Selection */}
            <FormControl fullWidth>
              <InputLabel>Export Format</InputLabel>
              <Select
                value={format}
                label="Export Format"
                onChange={(e) => setFormat(e.target.value as any)}
              >
                {formatOptions.map((option) => (
                  <MenuItem key={option.value} value={option.value}>
                    <Box display="flex" alignItems="center" gap={1}>
                      {option.icon}
                      <Box>
                        <Typography variant="body2">{option.label}</Typography>
                        <Typography variant="caption" color="text.secondary">
                          {option.description}
                        </Typography>
                      </Box>
                    </Box>
                  </MenuItem>
                ))}
              </Select>
            </FormControl>

            {/* Options */}
            <Box>
              <Typography variant="subtitle2" gutterBottom>
                Export Options
              </Typography>
              
              <FormControlLabel
                control={
                  <Checkbox
                    checked={includeSources}
                    onChange={(e) => setIncludeSources(e.target.checked)}
                  />
                }
                label="Include source information"
              />
              
              <FormControlLabel
                control={
                  <Checkbox
                    checked={includeMetadata}
                    onChange={(e) => setIncludeMetadata(e.target.checked)}
                  />
                }
                label="Include metadata and timestamps"
              />
            </Box>

            {/* Date Range (for history, companies, filings) */}
            {exportType !== 'query' && (
              <Box>
                <Typography variant="subtitle2" gutterBottom>
                  Date Range (Optional)
                </Typography>
                
                <Box display="flex" gap={2}>
                  <DatePicker
                    label="Start Date"
                    value={startDate}
                    onChange={setStartDate}
                    slotProps={{
                      textField: {
                        size: "small"
                      }
                    }}
                  />

                  <DatePicker
                    label="End Date"
                    value={endDate}
                    onChange={setEndDate}
                    slotProps={{
                      textField: {
                        size: "small"
                      }
                    }}
                  />
                </Box>
              </Box>
            )}

            {/* Summary */}
            <Box>
              <Divider sx={{ mb: 2 }} />
              <Typography variant="subtitle2" gutterBottom>
                Export Summary
              </Typography>
              
              <Box display="flex" flexWrap="wrap" gap={1}>
                <Chip label={`Format: ${format.toUpperCase()}`} size="small" />
                {includeSources && <Chip label="With Sources" size="small" color="primary" />}
                {includeMetadata && <Chip label="With Metadata" size="small" color="primary" />}
                {exportType === 'history' && (
                  <Chip label={`${queryHistory.length} queries`} size="small" />
                )}
              </Box>
            </Box>
          </Box>
        </DialogContent>

        <DialogActions>
          <Button onClick={handleClose} startIcon={<CloseIcon />}>
            Cancel
          </Button>
          
          <Button
            onClick={handleExport}
            variant="contained"
            startIcon={<DownloadIcon />}
            disabled={isExporting || exportSuccess}
          >
            {isExporting ? 'Exporting...' : 'Export'}
          </Button>
        </DialogActions>
      </Dialog>
    </LocalizationProvider>
  );
};

export default ExportDialog;
