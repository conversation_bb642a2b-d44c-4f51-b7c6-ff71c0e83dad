/**
 * Query Results Display Component
 * Phase 6: Frontend Development - Task 6.1
 */

import React, { useState } from 'react';
import {
  Box,
  Paper,
  Typography,
  Chip,
  Card,
  CardContent,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  IconButton,
  Tooltip,
  Alert,
  LinearProgress,
  Divider,
  Link,
} from '@mui/material';
import {
  ExpandMore as ExpandMoreIcon,
  ContentCopy as CopyIcon,
  OpenInNew as OpenIcon,
  TrendingUp as TrendingUpIcon,
  Assessment as AssessmentIcon,
  Schedule as ScheduleIcon,
  Business as BusinessIcon,
  Star as StarIcon,
  StarBorder as StarBorderIcon,
} from '@mui/icons-material';
import { QueryResponse } from '../types/api';

interface QueryResultsProps {
  result: QueryResponse;
  onBookmark?: (query: string, answer: string) => void;
  isBookmarked?: boolean;
}

const QueryResults: React.FC<QueryResultsProps> = ({
  result,
  onBookmark,
  isBookmarked = false,
}) => {
  const [expandedSources, setExpandedSources] = useState<number[]>([]);
  const [copiedText, setCopiedText] = useState<string | null>(null);

  // Handle source expansion
  const handleSourceToggle = (index: number) => {
    setExpandedSources(prev => 
      prev.includes(index) 
        ? prev.filter(i => i !== index)
        : [...prev, index]
    );
  };

  // Handle copy to clipboard
  const handleCopy = async (text: string, label: string) => {
    try {
      await navigator.clipboard.writeText(text);
      setCopiedText(label);
      setTimeout(() => setCopiedText(null), 2000);
    } catch (error) {
      console.error('Failed to copy text:', error);
    }
  };

  // Handle bookmark
  const handleBookmark = () => {
    if (onBookmark) {
      onBookmark(result.answer, result.answer); // TODO: Pass original query
    }
  };

  // Get confidence color
  const getConfidenceColor = (confidence: string) => {
    switch (confidence.toLowerCase()) {
      case 'high': return 'success';
      case 'medium': return 'warning';
      case 'low': return 'error';
      default: return 'info';
    }
  };

  // Get confidence score (0-100)
  const getConfidenceScore = (confidence: string) => {
    switch (confidence.toLowerCase()) {
      case 'high': return 85;
      case 'medium': return 65;
      case 'low': return 35;
      default: return 50;
    }
  };

  if (!result.success) {
    return (
      <Paper elevation={2} sx={{ p: 3, mb: 3 }}>
        <Alert severity="error">
          <Typography variant="h6" gutterBottom>
            Query Failed
          </Typography>
          <Typography>
            {result.error || 'An unexpected error occurred while processing your query.'}
          </Typography>
          
          {result.suggestions && result.suggestions.length > 0 && (
            <Box mt={2}>
              <Typography variant="subtitle2" gutterBottom>
                Suggestions:
              </Typography>
              <ul>
                {result.suggestions.map((suggestion, index) => (
                  <li key={index}>
                    <Typography variant="body2">{suggestion}</Typography>
                  </li>
                ))}
              </ul>
            </Box>
          )}
        </Alert>
      </Paper>
    );
  }

  return (
    <Box>
      {/* Main Answer */}
      <Paper elevation={3} sx={{ p: 4, mb: 3 }}>
        <Box display="flex" justifyContent="space-between" alignItems="flex-start" mb={3}>
          <Typography variant="h5" component="h2" gutterBottom>
            Answer
          </Typography>
          
          <Box display="flex" gap={1}>
            <Tooltip title={copiedText === 'answer' ? 'Copied!' : 'Copy answer'}>
              <IconButton
                onClick={() => handleCopy(result.answer, 'answer')}
                size="small"
              >
                <CopyIcon />
              </IconButton>
            </Tooltip>
            
            {onBookmark && (
              <Tooltip title={isBookmarked ? 'Remove bookmark' : 'Bookmark this answer'}>
                <IconButton
                  onClick={handleBookmark}
                  size="small"
                  color={isBookmarked ? 'primary' : 'default'}
                >
                  {isBookmarked ? <StarIcon /> : <StarBorderIcon />}
                </IconButton>
              </Tooltip>
            )}
          </Box>
        </Box>

        <Typography variant="body1" sx={{ lineHeight: 1.7, mb: 3 }}>
          {result.answer}
        </Typography>

        {/* Query Metadata */}
        <Box display="flex" flexWrap="wrap" gap={2} alignItems="center">
          <Chip
            icon={<AssessmentIcon />}
            label={`Model: ${result.model_used || 'Unknown'}`}
            size="small"
            variant="outlined"
          />
          
          <Chip
            icon={<ScheduleIcon />}
            label={`${result.processing_time?.toFixed(2) || 0}s`}
            size="small"
            variant="outlined"
          />
          
          <Chip
            icon={<BusinessIcon />}
            label={`${result.context_chunks || 0} sources`}
            size="small"
            variant="outlined"
          />

          {result.confidence && (
            <Tooltip title={`Confidence: ${result.confidence}`}>
              <Box display="flex" alignItems="center" gap={1}>
                <Typography variant="caption" color="text.secondary">
                  Confidence:
                </Typography>
                <Chip
                  label={result.confidence}
                  size="small"
                  color={getConfidenceColor(result.confidence) as any}
                  variant="filled"
                />
                <LinearProgress
                  variant="determinate"
                  value={getConfidenceScore(result.confidence)}
                  sx={{ width: 60, height: 4 }}
                  color={getConfidenceColor(result.confidence) as any}
                />
              </Box>
            </Tooltip>
          )}
        </Box>
      </Paper>

      {/* Sources */}
      {result.sources && result.sources.length > 0 && (
        <Paper elevation={2} sx={{ p: 3 }}>
          <Typography variant="h6" gutterBottom>
            Sources ({result.sources.length})
          </Typography>
          
          <Typography variant="body2" color="text.secondary" gutterBottom>
            Information retrieved from the following SEC filings:
          </Typography>

          <Box display="flex" flexDirection="column" gap={2}>
            {result.sources.map((source, index) => (
              <Card key={index} variant="outlined">
                <CardContent sx={{ pb: 2 }}>
                  <Box display="flex" justifyContent="between" alignItems="flex-start" mb={2}>
                    <Box flexGrow={1}>
                      <Typography variant="subtitle1" component="div" gutterBottom>
                        <strong>{source.ticker}</strong> - {source.filing_type}
                      </Typography>
                      
                      <Box display="flex" flexWrap="wrap" gap={1} mb={2}>
                        <Chip
                          label={source.filing_date}
                          size="small"
                          icon={<ScheduleIcon />}
                        />
                        
                        <Chip
                          label={source.section}
                          size="small"
                          variant="outlined"
                        />
                        
                        <Chip
                          label={`${(source.relevance_score * 100).toFixed(1)}% relevant`}
                          size="small"
                          color="primary"
                          variant="outlined"
                        />

                        {source.has_financial_data && (
                          <Chip
                            label="Financial Data"
                            size="small"
                            icon={<TrendingUpIcon />}
                            color="success"
                            variant="outlined"
                          />
                        )}
                      </Box>
                    </Box>

                    <Box display="flex" gap={1}>
                      <Tooltip title="Copy source info">
                        <IconButton
                          size="small"
                          onClick={() => handleCopy(
                            `${source.ticker} ${source.filing_type} (${source.filing_date}) - ${source.section}`,
                            `source-${index}`
                          )}
                        >
                          <CopyIcon fontSize="small" />
                        </IconButton>
                      </Tooltip>
                      
                      <Tooltip title="View filing">
                        <IconButton
                          size="small"
                          component={Link}
                          href={`https://www.sec.gov/edgar/search/#/q=${source.ticker}&dateRange=all`}
                          target="_blank"
                          rel="noopener noreferrer"
                        >
                          <OpenIcon fontSize="small" />
                        </IconButton>
                      </Tooltip>
                    </Box>
                  </Box>

                  {/* Content Preview */}
                  {source.content_preview && (
                    <Accordion 
                      expanded={expandedSources.includes(index)}
                      onChange={() => handleSourceToggle(index)}
                      elevation={0}
                      sx={{ bgcolor: 'transparent' }}
                    >
                      <AccordionSummary
                        expandIcon={<ExpandMoreIcon />}
                        sx={{ px: 0, minHeight: 'auto' }}
                      >
                        <Typography variant="body2" color="primary">
                          {expandedSources.includes(index) ? 'Hide' : 'Show'} content preview
                        </Typography>
                      </AccordionSummary>
                      
                      <AccordionDetails sx={{ px: 0, pt: 0 }}>
                        <Divider sx={{ mb: 2 }} />
                        <Typography 
                          variant="body2" 
                          sx={{ 
                            fontFamily: 'monospace',
                            bgcolor: 'grey.50',
                            p: 2,
                            borderRadius: 1,
                            whiteSpace: 'pre-wrap',
                            fontSize: '0.875rem',
                            lineHeight: 1.5,
                          }}
                        >
                          {source.content_preview}
                        </Typography>
                      </AccordionDetails>
                    </Accordion>
                  )}
                </CardContent>
              </Card>
            ))}
          </Box>

          {/* Source Summary */}
          <Box mt={3} p={2} bgcolor="info.light" borderRadius={1}>
            <Typography variant="caption" display="block" gutterBottom>
              <strong>Source Summary:</strong>
            </Typography>
            <Typography variant="caption" color="text.secondary">
              Retrieved {result.sources.length} relevant sections from SEC filings. 
              Average relevance score: {(result.sources.reduce((sum, s) => sum + s.relevance_score, 0) / result.sources.length * 100).toFixed(1)}%.
              {result.sources.some(s => s.has_financial_data) && ' Includes financial data.'}
            </Typography>
          </Box>
        </Paper>
      )}

      {/* Query Analysis (if available) */}
      {result.query_analysis && (
        <Paper elevation={1} sx={{ p: 3, mt: 3, bgcolor: 'grey.50' }}>
          <Typography variant="h6" gutterBottom>
            Query Analysis
          </Typography>
          
          <Box display="flex" flexWrap="wrap" gap={1}>
            <Chip label={`Intent: ${result.query_analysis.intent}`} size="small" />
            
            {result.query_analysis.tickers.length > 0 && (
              <Chip 
                label={`Companies: ${result.query_analysis.tickers.join(', ')}`} 
                size="small" 
                color="primary"
              />
            )}
            
            {result.query_analysis.filing_types.length > 0 && (
              <Chip 
                label={`Filings: ${result.query_analysis.filing_types.join(', ')}`} 
                size="small" 
                color="secondary"
              />
            )}
            
            <Chip 
              label={`Type: ${result.query_analysis.question_type}`} 
              size="small" 
              variant="outlined"
            />

            {result.query_analysis.has_comparison && (
              <Chip 
                label="Comparison Query" 
                size="small" 
                color="warning"
                variant="outlined"
              />
            )}
          </Box>
        </Paper>
      )}

      {/* Copy Success Feedback */}
      {copiedText && (
        <Alert severity="success" sx={{ mt: 2 }}>
          {copiedText === 'answer' ? 'Answer' : 'Source information'} copied to clipboard!
        </Alert>
      )}
    </Box>
  );
};

export default QueryResults;
