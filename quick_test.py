#!/usr/bin/env python3
"""
Quick Test Script
Fast verification that both servers are working
"""

import requests
import json
import time

def test_backend():
    """Quick backend test"""
    print("🔍 Testing Backend...")
    
    try:
        # Health check
        response = requests.get("http://127.0.0.1:8000/api/v1/health/", timeout=5)
        if response.status_code == 200:
            print("  ✅ Health Check: PASSED")
        else:
            print(f"  ❌ Health Check: FAILED ({response.status_code})")
            return False
        
        # Companies endpoint
        response = requests.get("http://127.0.0.1:8000/api/v1/companies/?page_size=1", timeout=5)
        if response.status_code == 200:
            data = response.json()
            print(f"  ✅ Companies API: PASSED ({data.get('total', 0)} companies)")
        else:
            print(f"  ❌ Companies API: FAILED ({response.status_code})")
        
        # Query status
        response = requests.get("http://127.0.0.1:8000/api/v1/query/status", timeout=5)
        if response.status_code == 200:
            data = response.json()
            print(f"  ✅ Query Engine: PASSED ({data.get('supported_companies', 0)} supported)")
        else:
            print(f"  ❌ Query Engine: FAILED ({response.status_code})")
        
        return True
        
    except requests.exceptions.ConnectionError:
        print("  ❌ Backend: NOT RUNNING")
        print("     Start with: python start_servers.py")
        return False
    except Exception as e:
        print(f"  ❌ Backend Error: {str(e)}")
        return False

def test_frontend():
    """Quick frontend test"""
    print("🌐 Testing Frontend...")
    
    try:
        response = requests.get("http://localhost:3000", timeout=5)
        if response.status_code == 200:
            print("  ✅ Frontend Server: RUNNING")
            return True
        else:
            print(f"  ❌ Frontend Server: FAILED ({response.status_code})")
            return False
            
    except requests.exceptions.ConnectionError:
        print("  ❌ Frontend: NOT RUNNING")
        print("     Start with: python start_servers.py")
        return False
    except Exception as e:
        print(f"  ❌ Frontend Error: {str(e)}")
        return False

def test_query():
    """Test a simple query"""
    print("🤖 Testing Query Processing...")
    
    try:
        query_data = {
            "question": "What is Apple's business model?",
            "max_chunks": 2,
            "include_sources": True,
            "include_confidence": True
        }
        
        print("  🚀 Submitting test query...")
        start_time = time.time()
        
        response = requests.post(
            "http://127.0.0.1:8000/api/v1/query/",
            json=query_data,
            timeout=30
        )
        
        processing_time = time.time() - start_time
        
        if response.status_code == 200:
            result = response.json()
            print(f"  ✅ Query Processing: SUCCESS ({processing_time:.2f}s)")
            print(f"     Success: {result.get('success')}")
            print(f"     Model: {result.get('model_used', 'unknown')}")
            print(f"     Sources: {len(result.get('sources', []))}")
            
            answer = result.get('answer', '')
            if answer:
                preview = answer[:100] + "..." if len(answer) > 100 else answer
                print(f"     Answer: {preview}")
            else:
                print(f"     Answer: No answer generated")
            
            return True
        else:
            print(f"  ❌ Query Processing: FAILED ({response.status_code})")
            try:
                error_data = response.json()
                print(f"     Error: {error_data.get('detail', 'Unknown error')}")
            except:
                pass
            return False
            
    except requests.exceptions.ConnectionError:
        print("  ❌ Query Processing: Backend not available")
        return False
    except Exception as e:
        print(f"  ❌ Query Processing Error: {str(e)}")
        return False

def main():
    """Main test function"""
    print("⚡ QUICK TEST - SEC Filing QA Agent")
    print("=" * 40)
    print()
    
    backend_ok = test_backend()
    print()
    
    frontend_ok = test_frontend()
    print()
    
    if backend_ok:
        query_ok = test_query()
        print()
    else:
        query_ok = False
    
    # Summary
    print("📊 TEST SUMMARY")
    print("-" * 20)
    print(f"Backend:  {'✅ PASS' if backend_ok else '❌ FAIL'}")
    print(f"Frontend: {'✅ PASS' if frontend_ok else '❌ FAIL'}")
    print(f"Queries:  {'✅ PASS' if query_ok else '❌ FAIL'}")
    print()
    
    if backend_ok and frontend_ok:
        print("🎉 SYSTEM READY!")
        print("🌐 Open: http://localhost:3000")
        print("📚 API Docs: http://127.0.0.1:8000/docs")
        print()
        print("💡 Try asking questions like:")
        print("   - What are Apple's main revenue sources?")
        print("   - How did Microsoft perform last quarter?")
        print("   - What are Tesla's risk factors?")
    else:
        print("❌ SYSTEM NOT READY")
        print("🔧 Run: python start_servers.py")
    
    return backend_ok and frontend_ok

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
