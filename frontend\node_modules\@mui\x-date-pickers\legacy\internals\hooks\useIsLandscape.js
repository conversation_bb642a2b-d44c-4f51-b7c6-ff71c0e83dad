import _slicedToArray from "@babel/runtime/helpers/esm/slicedToArray";
import * as React from 'react';
import { unstable_useEnhancedEffect as useEnhancedEffect } from '@mui/utils';
import { arrayIncludes } from '../utils/utils';
function getOrientation() {
  if (typeof window === 'undefined') {
    return 'portrait';
  }
  if (window.screen && window.screen.orientation && window.screen.orientation.angle) {
    return Math.abs(window.screen.orientation.angle) === 90 ? 'landscape' : 'portrait';
  }

  // Support IOS safari
  if (window.orientation) {
    return Math.abs(Number(window.orientation)) === 90 ? 'landscape' : 'portrait';
  }
  return 'portrait';
}
export var useIsLandscape = function useIsLandscape(views, customOrientation) {
  var _React$useState = React.useState(getOrientation),
    _React$useState2 = _slicedToArray(_React$useState, 2),
    orientation = _React$useState2[0],
    setOrientation = _React$useState2[1];
  useEnhancedEffect(function () {
    var eventHandler = function eventHandler() {
      setOrientation(getOrientation());
    };
    window.addEventListener('orientationchange', eventHandler);
    return function () {
      window.removeEventListener('orientationchange', eventHandler);
    };
  }, []);
  if (arrayIncludes(views, ['hours', 'minutes', 'seconds'])) {
    // could not display 13:34:44 in landscape mode
    return false;
  }
  var orientationToUse = customOrientation || orientation;
  return orientationToUse === 'landscape';
};