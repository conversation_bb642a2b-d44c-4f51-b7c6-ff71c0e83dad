import { getPickersLocalization } from './utils/getPickersLocalization';
var views = {
  hours: '小时',
  minutes: '分钟',
  seconds: '秒',
  meridiem: '十二小时制'
};
var zhCNPickers = {
  // Calendar navigation
  previousMonth: '上个月',
  nextMonth: '下个月',
  // View navigation
  openPreviousView: '前一个视图',
  openNextView: '下一个视图',
  calendarViewSwitchingButtonAriaLabel: function calendarViewSwitchingButtonAriaLabel(view) {
    return view === 'year' ? '年视图已打开，切换为日历视图' : '日历视图已打开，切换为年视图';
  },
  // DateRange placeholders
  start: '开始',
  end: '结束',
  // Action bar
  cancelButtonLabel: '取消',
  clearButtonLabel: '清除',
  okButtonLabel: '确认',
  todayButtonLabel: '今天',
  // Toolbar titles
  datePickerToolbarTitle: '选择日期',
  dateTimePickerToolbarTitle: '选择日期和时间',
  timePickerToolbarTitle: '选择时间',
  dateRangePickerToolbarTitle: '选择时间范围',
  // Clock labels
  clockLabelText: function clockLabelText(view, time, adapter) {
    return "\u9009\u62E9 ".concat(views[view], ". ").concat(time === null ? '未选择时间' : "\u5DF2\u9009\u62E9".concat(adapter.format(time, 'fullTime')));
  },
  hoursClockNumberText: function hoursClockNumberText(hours) {
    return "".concat(hours, "\u5C0F\u65F6");
  },
  minutesClockNumberText: function minutesClockNumberText(minutes) {
    return "".concat(minutes, "\u5206\u949F");
  },
  secondsClockNumberText: function secondsClockNumberText(seconds) {
    return "".concat(seconds, "\u79D2");
  },
  // Digital clock labels
  selectViewText: function selectViewText(view) {
    return "\u9009\u62E9 ".concat(views[view]);
  },
  // Calendar labels
  calendarWeekNumberHeaderLabel: '周数',
  calendarWeekNumberHeaderText: '#',
  calendarWeekNumberAriaLabelText: function calendarWeekNumberAriaLabelText(weekNumber) {
    return "\u7B2C".concat(weekNumber, "\u5468");
  },
  calendarWeekNumberText: function calendarWeekNumberText(weekNumber) {
    return "".concat(weekNumber);
  },
  // Open picker labels
  openDatePickerDialogue: function openDatePickerDialogue(value, utils) {
    return value !== null && utils.isValid(value) ? "\u9009\u62E9\u65E5\u671F\uFF0C\u5DF2\u9009\u62E9".concat(utils.format(value, 'fullDate')) : '选择日期';
  },
  openTimePickerDialogue: function openTimePickerDialogue(value, utils) {
    return value !== null && utils.isValid(value) ? "\u9009\u62E9\u65F6\u95F4\uFF0C\u5DF2\u9009\u62E9".concat(utils.format(value, 'fullTime')) : '选择时间';
  },
  fieldClearLabel: '清除',
  // Table labels
  timeTableLabel: '选择时间',
  dateTableLabel: '选择日期',
  // Field section placeholders
  fieldYearPlaceholder: function fieldYearPlaceholder(params) {
    return 'Y'.repeat(params.digitAmount);
  },
  fieldMonthPlaceholder: function fieldMonthPlaceholder(params) {
    return params.contentType === 'letter' ? 'MMMM' : 'MM';
  },
  fieldDayPlaceholder: function fieldDayPlaceholder() {
    return 'DD';
  },
  fieldWeekDayPlaceholder: function fieldWeekDayPlaceholder(params) {
    return params.contentType === 'letter' ? 'EEEE' : 'EE';
  },
  fieldHoursPlaceholder: function fieldHoursPlaceholder() {
    return 'hh';
  },
  fieldMinutesPlaceholder: function fieldMinutesPlaceholder() {
    return 'mm';
  },
  fieldSecondsPlaceholder: function fieldSecondsPlaceholder() {
    return 'ss';
  },
  fieldMeridiemPlaceholder: function fieldMeridiemPlaceholder() {
    return 'aa';
  }
};
export var zhCN = getPickersLocalization(zhCNPickers);