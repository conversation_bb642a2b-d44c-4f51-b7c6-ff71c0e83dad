{"name": "sec-filings-qa-frontend", "private": true, "version": "1.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "test": "jest"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "@mui/material": "^5.14.18", "@mui/icons-material": "^5.14.18", "@mui/x-date-pickers": "^6.18.1", "@emotion/react": "^11.11.1", "@emotion/styled": "^11.11.0", "@tanstack/react-query": "^5.8.4", "axios": "^1.6.2", "react-router-dom": "^6.18.0", "date-fns": "^2.30.0"}, "devDependencies": {"@types/react": "^18.2.37", "@types/react-dom": "^18.2.15", "@typescript-eslint/eslint-plugin": "^6.10.0", "@typescript-eslint/parser": "^6.10.0", "@vitejs/plugin-react": "^4.1.1", "eslint": "^8.53.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.4", "typescript": "^5.2.2", "vite": "^4.5.0", "@testing-library/react": "^13.4.0", "@testing-library/jest-dom": "^6.1.4", "@testing-library/user-event": "^14.5.1", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0"}}