/**
 * React Query API Hooks
 * Phase 6: Frontend Development - Task 6.2
 */

import { useQuery, useMutation, useQueryClient, UseQueryOptions, UseMutationOptions } from '@tanstack/react-query';
import { useCallback } from 'react';
import api from '../services/api';
import {
  QueryRequest,
  QueryResponse,
  Company,
  CompanyListResponse,
  FilingListResponse,
  HealthResponse,
  BatchQueryRequest,
  BatchResponse,
  QueryHistoryEntry,
  HistoryStats,
  APIError,
} from '../types/api';

// Query Keys
export const queryKeys = {
  // Health
  health: ['health'] as const,
  healthDetailed: ['health', 'detailed'] as const,
  
  // Companies
  companies: ['companies'] as const,
  companiesList: (params?: any) => ['companies', 'list', params] as const,
  company: (ticker: string) => ['companies', ticker] as const,
  companyFilings: (ticker: string) => ['companies', ticker, 'filings'] as const,
  supportedCompanies: ['companies', 'supported'] as const,
  
  // Filings
  filings: ['filings'] as const,
  filingsList: (params?: any) => ['filings', 'list', params] as const,
  companySpecificFilings: (ticker: string, type: string) => ['filings', ticker, type] as const,
  
  // Query Engine
  queryStatus: ['query', 'status'] as const,
  queryModels: ['query', 'models'] as const,
  
  // Batch Processing
  batches: ['batches'] as const,
  batchStatus: (batchId: string) => ['batches', batchId] as const,
  
  // Admin
  cacheStats: ['admin', 'cache', 'stats'] as const,
  historyStats: (params?: any) => ['admin', 'history', 'stats', params] as const,
  queryHistory: (params?: any) => ['admin', 'history', params] as const,
  systemStatus: ['admin', 'system', 'status'] as const,
};

// Health Hooks
export const useHealth = (options?: UseQueryOptions<HealthResponse, APIError>) => {
  return useQuery({
    queryKey: queryKeys.health,
    queryFn: api.health.getHealth,
    staleTime: 30 * 1000, // 30 seconds
    refetchInterval: 60 * 1000, // 1 minute
    ...options,
  });
};

export const useDetailedHealth = (options?: UseQueryOptions<HealthResponse, APIError>) => {
  return useQuery({
    queryKey: queryKeys.healthDetailed,
    queryFn: api.health.getDetailedHealth,
    staleTime: 30 * 1000, // 30 seconds
    ...options,
  });
};

// Companies Hooks
export const useCompanies = (
  params?: Parameters<typeof api.companies.getCompanies>[0],
  options?: UseQueryOptions<CompanyListResponse, APIError>
) => {
  return useQuery({
    queryKey: queryKeys.companiesList(params),
    queryFn: () => api.companies.getCompanies(params),
    staleTime: 5 * 60 * 1000, // 5 minutes
    ...options,
  });
};

export const useCompany = (
  ticker: string,
  options?: UseQueryOptions<Company, APIError>
) => {
  return useQuery({
    queryKey: queryKeys.company(ticker),
    queryFn: () => api.companies.getCompany(ticker),
    enabled: !!ticker,
    staleTime: 5 * 60 * 1000, // 5 minutes
    ...options,
  });
};

export const useSupportedCompanies = (options?: UseQueryOptions<string[], APIError>) => {
  return useQuery({
    queryKey: queryKeys.supportedCompanies,
    queryFn: api.query.getSupportedCompanies,
    staleTime: 10 * 60 * 1000, // 10 minutes
    ...options,
  });
};

export const useCompanySearch = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: api.companies.searchCompanies,
    onSuccess: (data) => {
      // Cache individual companies
      data.companies.forEach(company => {
        queryClient.setQueryData(queryKeys.company(company.ticker), company);
      });
    },
  });
};

// Filings Hooks
export const useFilings = (
  params?: Parameters<typeof api.filings.getFilings>[0],
  options?: UseQueryOptions<FilingListResponse, APIError>
) => {
  return useQuery({
    queryKey: queryKeys.filingsList(params),
    queryFn: () => api.filings.getFilings(params),
    staleTime: 5 * 60 * 1000, // 5 minutes
    ...options,
  });
};

export const useCompanyFilings = (
  ticker: string,
  filingType: string,
  limit?: number,
  options?: UseQueryOptions<any, APIError>
) => {
  return useQuery({
    queryKey: queryKeys.companySpecificFilings(ticker, filingType),
    queryFn: () => api.filings.getCompanyFilings(ticker, filingType, limit),
    enabled: !!ticker && !!filingType,
    staleTime: 5 * 60 * 1000, // 5 minutes
    ...options,
  });
};

// Query Engine Hooks
export const useQueryStatus = (options?: UseQueryOptions<any, APIError>) => {
  return useQuery({
    queryKey: queryKeys.queryStatus,
    queryFn: api.query.getStatus,
    staleTime: 2 * 60 * 1000, // 2 minutes
    ...options,
  });
};

export const useQueryModels = (options?: UseQueryOptions<any, APIError>) => {
  return useQuery({
    queryKey: queryKeys.queryModels,
    queryFn: api.query.getModels,
    staleTime: 10 * 60 * 1000, // 10 minutes
    ...options,
  });
};

// Main Query Mutation
export const useSubmitQuery = (
  options?: UseMutationOptions<QueryResponse, APIError, QueryRequest>
) => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: api.query.submitQuery,
    onSuccess: (data) => {
      // Invalidate related queries if needed
      if (data.success) {
        // Could invalidate cache stats if caching was used
        queryClient.invalidateQueries({ queryKey: queryKeys.cacheStats });
      }
    },
    ...options,
  });
};

// Batch Processing Hooks
export const useBatches = (
  limit?: number,
  offset?: number,
  options?: UseQueryOptions<any, APIError>
) => {
  return useQuery({
    queryKey: queryKeys.batches,
    queryFn: () => api.batch.listBatches(limit, offset),
    staleTime: 30 * 1000, // 30 seconds
    ...options,
  });
};

export const useBatchStatus = (
  batchId: string,
  options?: UseQueryOptions<BatchResponse, APIError>
) => {
  return useQuery({
    queryKey: queryKeys.batchStatus(batchId),
    queryFn: () => api.batch.getBatchStatus(batchId),
    enabled: !!batchId,
    refetchInterval: (query) => {
      // Stop polling when batch is completed or failed
      return query.state.data?.status === 'processing' ? 2000 : false;
    },
    ...options,
  });
};

export const useSubmitBatch = (
  options?: UseMutationOptions<BatchResponse, APIError, BatchQueryRequest>
) => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: api.batch.submitBatch,
    onSuccess: () => {
      // Invalidate batches list
      queryClient.invalidateQueries({ queryKey: queryKeys.batches });
    },
    ...options,
  });
};

export const useUploadBatchFile = (
  options?: UseMutationOptions<any, APIError, File>
) => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: api.batch.uploadBatchFile,
    onSuccess: () => {
      // Invalidate batches list
      queryClient.invalidateQueries({ queryKey: queryKeys.batches });
    },
    ...options,
  });
};

// Admin Hooks
export const useCacheStats = (options?: UseQueryOptions<any, APIError>) => {
  return useQuery({
    queryKey: queryKeys.cacheStats,
    queryFn: api.admin.getCacheStats,
    staleTime: 30 * 1000, // 30 seconds
    ...options,
  });
};

export const useHistoryStats = (
  days?: number,
  userId?: string,
  options?: UseQueryOptions<HistoryStats, APIError>
) => {
  const params = { days, userId };
  return useQuery({
    queryKey: queryKeys.historyStats(params),
    queryFn: () => api.admin.getHistoryStats(days, userId),
    staleTime: 2 * 60 * 1000, // 2 minutes
    ...options,
  });
};

export const useQueryHistory = (
  params?: Parameters<typeof api.admin.getQueryHistory>[0],
  options?: UseQueryOptions<{ history: QueryHistoryEntry[]; count: number }, APIError>
) => {
  return useQuery({
    queryKey: queryKeys.queryHistory(params),
    queryFn: () => api.admin.getQueryHistory(params),
    staleTime: 60 * 1000, // 1 minute
    ...options,
  });
};

export const useSystemStatus = (options?: UseQueryOptions<any, APIError>) => {
  return useQuery({
    queryKey: queryKeys.systemStatus,
    queryFn: api.admin.getSystemStatus,
    staleTime: 30 * 1000, // 30 seconds
    refetchInterval: 60 * 1000, // 1 minute
    ...options,
  });
};

// Cache Management Mutations
export const useClearCache = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: api.admin.clearCache,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: queryKeys.cacheStats });
    },
  });
};

export const useCleanupExpiredCache = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: api.admin.cleanupExpiredCache,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: queryKeys.cacheStats });
    },
  });
};

// Utility Hooks
export const useApiConnection = () => {
  const { data: health, isLoading, error } = useHealth();
  
  const isConnected = health?.status === 'healthy';
  const isOffline = !!error;
  
  return {
    isConnected,
    isOffline,
    isLoading,
    health,
    error,
  };
};

// Prefetch utilities
export const usePrefetchCompanies = () => {
  const queryClient = useQueryClient();
  
  return useCallback(
    (params?: Parameters<typeof api.companies.getCompanies>[0]) => {
      queryClient.prefetchQuery({
        queryKey: queryKeys.companiesList(params),
        queryFn: () => api.companies.getCompanies(params),
        staleTime: 5 * 60 * 1000,
      });
    },
    [queryClient]
  );
};

export const usePrefetchCompany = () => {
  const queryClient = useQueryClient();
  
  return useCallback(
    (ticker: string) => {
      if (ticker) {
        queryClient.prefetchQuery({
          queryKey: queryKeys.company(ticker),
          queryFn: () => api.companies.getCompany(ticker),
          staleTime: 5 * 60 * 1000,
        });
      }
    },
    [queryClient]
  );
};

// Optimistic updates
export const useOptimisticQueryUpdate = () => {
  const queryClient = useQueryClient();
  
  return useCallback(
    (queryKey: any[], updater: (old: any) => any) => {
      queryClient.setQueryData(queryKey, updater);
    },
    [queryClient]
  );
};

export default {
  // Health
  useHealth,
  useDetailedHealth,
  
  // Companies
  useCompanies,
  useCompany,
  useSupportedCompanies,
  useCompanySearch,
  
  // Filings
  useFilings,
  useCompanyFilings,
  
  // Query Engine
  useQueryStatus,
  useQueryModels,
  useSubmitQuery,
  
  // Batch Processing
  useBatches,
  useBatchStatus,
  useSubmitBatch,
  useUploadBatchFile,
  
  // Admin
  useCacheStats,
  useHistoryStats,
  useQueryHistory,
  useSystemStatus,
  useClearCache,
  useCleanupExpiredCache,
  
  // Utilities
  useApiConnection,
  usePrefetchCompanies,
  usePrefetchCompany,
  useOptimisticQueryUpdate,
};
