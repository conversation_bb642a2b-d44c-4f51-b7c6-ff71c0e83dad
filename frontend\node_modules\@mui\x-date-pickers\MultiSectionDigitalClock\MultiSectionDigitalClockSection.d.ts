import * as React from 'react';
import { MultiSectionDigitalClockSectionClasses } from './multiSectionDigitalClockSectionClasses';
import type { MultiSectionDigitalClockOption, MultiSectionDigitalClockSlotsComponent, MultiSectionDigitalClockSlotsComponentsProps } from './MultiSectionDigitalClock.types';
import { UncapitalizeObjectKeys } from '../internals/utils/slots-migration';
export interface ExportedMultiSectionDigitalClockSectionProps {
    className?: string;
    classes?: Partial<MultiSectionDigitalClockSectionClasses>;
    slots?: UncapitalizeObjectKeys<MultiSectionDigitalClockSlotsComponent>;
    slotProps?: MultiSectionDigitalClockSlotsComponentsProps;
}
export interface MultiSectionDigitalClockSectionProps<TValue> extends ExportedMultiSectionDigitalClockSectionProps {
    autoFocus?: boolean;
    disabled?: boolean;
    readOnly?: boolean;
    items: MultiSectionDigitalClockOption<TValue>[];
    onChange: (value: TValue) => void;
    active?: boolean;
    skipDisabled?: boolean;
    role?: string;
}
type MultiSectionDigitalClockSectionComponent = <TValue>(props: MultiSectionDigitalClockSectionProps<TValue> & React.RefAttributes<HTMLUListElement>) => React.JSX.Element & {
    propTypes?: any;
};
/**
 * @ignore - internal component.
 */
export declare const MultiSectionDigitalClockSection: MultiSectionDigitalClockSectionComponent;
export {};
