import _extends from "@babel/runtime/helpers/esm/extends";
import _objectWithoutProperties from "@babel/runtime/helpers/esm/objectWithoutProperties";
var _excluded = ["props", "getOpenDialogAriaText"];
import * as React from 'react';
import { useSlotProps } from '@mui/base/utils';
import useForkRef from '@mui/utils/useForkRef';
import useId from '@mui/utils/useId';
import { PickersModalDialog } from '../../components/PickersModalDialog';
import { usePicker } from '../usePicker';
import { onSpaceOrEnter } from '../../utils/utils';
import { useUtils } from '../useUtils';
import { LocalizationProvider } from '../../../LocalizationProvider';
import { PickersLayout } from '../../../PickersLayout';
import { jsx as _jsx } from "react/jsx-runtime";
import { jsxs as _jsxs } from "react/jsx-runtime";
/**
 * Hook managing all the single-date mobile pickers:
 * - MobileDatePicker
 * - MobileDateTimePicker
 * - MobileTimePicker
 */
export var useMobilePicker = function useMobilePicker(_ref) {
  var _innerSlotProps$toolb, _innerSlotProps$toolb2, _slots$layout;
  var props = _ref.props,
    getOpenDialogAriaText = _ref.getOpenDialogAriaText,
    pickerParams = _objectWithoutProperties(_ref, _excluded);
  var slots = props.slots,
    innerSlotProps = props.slotProps,
    className = props.className,
    sx = props.sx,
    format = props.format,
    formatDensity = props.formatDensity,
    timezone = props.timezone,
    name = props.name,
    label = props.label,
    inputRef = props.inputRef,
    readOnly = props.readOnly,
    disabled = props.disabled,
    localeText = props.localeText;
  var utils = useUtils();
  var internalInputRef = React.useRef(null);
  var labelId = useId();
  var isToolbarHidden = (_innerSlotProps$toolb = innerSlotProps == null || (_innerSlotProps$toolb2 = innerSlotProps.toolbar) == null ? void 0 : _innerSlotProps$toolb2.hidden) != null ? _innerSlotProps$toolb : false;
  var _usePicker = usePicker(_extends({}, pickerParams, {
      props: props,
      inputRef: internalInputRef,
      autoFocusView: true,
      additionalViewProps: {},
      wrapperVariant: 'mobile'
    })),
    open = _usePicker.open,
    actions = _usePicker.actions,
    layoutProps = _usePicker.layoutProps,
    renderCurrentView = _usePicker.renderCurrentView,
    pickerFieldProps = _usePicker.fieldProps;
  var Field = slots.field;
  var fieldProps = useSlotProps({
    elementType: Field,
    externalSlotProps: innerSlotProps == null ? void 0 : innerSlotProps.field,
    additionalProps: _extends({}, pickerFieldProps, isToolbarHidden && {
      id: labelId
    }, !(disabled || readOnly) && {
      onClick: actions.onOpen,
      onKeyDown: onSpaceOrEnter(actions.onOpen)
    }, {
      readOnly: readOnly != null ? readOnly : true,
      disabled: disabled,
      className: className,
      sx: sx,
      format: format,
      formatDensity: formatDensity,
      timezone: timezone,
      label: label,
      name: name
    }),
    ownerState: props
  });

  // TODO: Move to `useSlotProps` when https://github.com/mui/material-ui/pull/35088 will be merged
  fieldProps.inputProps = _extends({}, fieldProps.inputProps, {
    'aria-label': getOpenDialogAriaText(pickerFieldProps.value, utils)
  });
  var slotsForField = _extends({
    textField: slots.textField
  }, fieldProps.slots);
  var Layout = (_slots$layout = slots.layout) != null ? _slots$layout : PickersLayout;
  var handleInputRef = useForkRef(internalInputRef, fieldProps.inputRef, inputRef);
  var labelledById = labelId;
  if (isToolbarHidden) {
    if (label) {
      labelledById = "".concat(labelId, "-label");
    } else {
      labelledById = undefined;
    }
  }
  var slotProps = _extends({}, innerSlotProps, {
    toolbar: _extends({}, innerSlotProps == null ? void 0 : innerSlotProps.toolbar, {
      titleId: labelId
    }),
    mobilePaper: _extends({
      'aria-labelledby': labelledById
    }, innerSlotProps == null ? void 0 : innerSlotProps.mobilePaper)
  });
  var renderPicker = function renderPicker() {
    return /*#__PURE__*/_jsxs(LocalizationProvider, {
      localeText: localeText,
      children: [/*#__PURE__*/_jsx(Field, _extends({}, fieldProps, {
        slots: slotsForField,
        slotProps: slotProps,
        inputRef: handleInputRef
      })), /*#__PURE__*/_jsx(PickersModalDialog, _extends({}, actions, {
        open: open,
        slots: slots,
        slotProps: slotProps,
        children: /*#__PURE__*/_jsx(Layout, _extends({}, layoutProps, slotProps == null ? void 0 : slotProps.layout, {
          slots: slots,
          slotProps: slotProps,
          children: renderCurrentView()
        }))
      }))]
    });
  };
  return {
    renderPicker: renderPicker
  };
};