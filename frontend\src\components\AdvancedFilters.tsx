/**
 * Advanced Filters Component with Mobile Responsiveness
 * Phase 6: Frontend Development - Task 6.3
 */

import React, { useState } from 'react';
import {
  Box,
  Paper,
  Typography,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  FormControl,
  InputLabel,
  Select,
  MenuItem,

  FormControlLabel,
  Switch,
  Chip,
  Button,
  IconButton,
  Grid,
  useTheme,
  useMediaQuery,
  Drawer,
  AppBar,
  Toolbar,
} from '@mui/material';
import {
  ExpandMore as ExpandMoreIcon,
  FilterList as FilterIcon,
  Clear as ClearIcon,
  Close as CloseIcon,
  Tune as TuneIcon,
  DateRange as DateRangeIcon,
  Business as BusinessIcon,
  Assessment as AssessmentIcon,
} from '@mui/icons-material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { QueryFilters, Company } from '../types/api';

interface AdvancedFiltersProps {
  filters: QueryFilters;
  onFiltersChange: (filters: QueryFilters) => void;
  companies: Company[];
  isLoading?: boolean;
  onClose?: () => void;
  mobile?: boolean;
}

const AdvancedFilters: React.FC<AdvancedFiltersProps> = ({
  filters,
  onFiltersChange,
  companies,
  onClose,
  mobile = false,
}) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const isSmallScreen = useMediaQuery(theme.breakpoints.down('sm'));

  // Local state for temporary filter values
  const [localFilters, setLocalFilters] = useState<QueryFilters>(filters);
  const [expandedSections, setExpandedSections] = useState<string[]>(['companies']);

  // Available options
  const filingTypes = [
    { value: '10-K', label: '10-K (Annual Report)' },
    { value: '10-Q', label: '10-Q (Quarterly Report)' },
    { value: '8-K', label: '8-K (Current Report)' },
    { value: 'DEF 14A', label: 'DEF 14A (Proxy Statement)' },
    { value: '20-F', label: '20-F (Foreign Annual Report)' },
    { value: '6-K', label: '6-K (Foreign Interim Report)' },
  ];



  const confidenceLevels = [
    { value: 'high', label: 'High (85%+)' },
    { value: 'medium', label: 'Medium (65%+)' },
    { value: 'low', label: 'Low (35%+)' },
  ];

  // Handle section expansion
  const handleSectionToggle = (section: string) => {
    setExpandedSections(prev =>
      prev.includes(section)
        ? prev.filter(s => s !== section)
        : [...prev, section]
    );
  };

  // Handle filter changes
  const handleFilterChange = (key: keyof QueryFilters, value: any) => {
    const newFilters = { ...localFilters, [key]: value };
    setLocalFilters(newFilters);
    onFiltersChange(newFilters);
  };

  // Handle date range changes
  const handleDateRangeChange = (field: 'start_date' | 'end_date', value: Date | null) => {
    const newDateRange = {
      ...localFilters.date_range,
      [field]: value?.toISOString().split('T')[0] || undefined,
    };
    handleFilterChange('date_range', newDateRange);
  };

  // Clear all filters
  const handleClearFilters = () => {
    const clearedFilters: QueryFilters = {};
    setLocalFilters(clearedFilters);
    onFiltersChange(clearedFilters);
  };

  // Check if any filters are active
  const hasActiveFilters = Boolean(
    localFilters.companies?.length ||
    localFilters.filing_types?.length ||
    localFilters.date_range?.start_date ||
    localFilters.date_range?.end_date ||
    localFilters.has_financial_data !== undefined ||
    localFilters.min_confidence
  );

  // Filter content component
  const FilterContent = () => (
    <LocalizationProvider dateAdapter={AdapterDateFns}>
      <Box sx={{ p: isMobile ? 2 : 0 }}>
        {/* Companies Filter */}
        <Accordion
          expanded={expandedSections.includes('companies')}
          onChange={() => handleSectionToggle('companies')}
        >
          <AccordionSummary expandIcon={<ExpandMoreIcon />}>
            <Box display="flex" alignItems="center" gap={1}>
              <BusinessIcon color="primary" />
              <Typography variant="subtitle1">Companies</Typography>
              {localFilters.companies?.length && (
                <Chip
                  label={localFilters.companies.length}
                  size="small"
                  color="primary"
                />
              )}
            </Box>
          </AccordionSummary>
          <AccordionDetails>
            <FormControl fullWidth size="small">
              <InputLabel>Select Companies</InputLabel>
              <Select
                multiple
                value={localFilters.companies || []}
                onChange={(e) => handleFilterChange('companies', e.target.value)}
                renderValue={(selected) => (
                  <Box display="flex" flexWrap="wrap" gap={0.5}>
                    {(selected as string[]).map((ticker) => (
                      <Chip key={ticker} label={ticker} size="small" />
                    ))}
                  </Box>
                )}
              >
                {companies.map((company) => (
                  <MenuItem key={company.ticker} value={company.ticker}>
                    <Box>
                      <Typography variant="body2">
                        <strong>{company.ticker}</strong> - {company.name}
                      </Typography>
                      <Typography variant="caption" color="text.secondary">
                        {company.sector}
                      </Typography>
                    </Box>
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </AccordionDetails>
        </Accordion>

        {/* Filing Types Filter */}
        <Accordion
          expanded={expandedSections.includes('filings')}
          onChange={() => handleSectionToggle('filings')}
        >
          <AccordionSummary expandIcon={<ExpandMoreIcon />}>
            <Box display="flex" alignItems="center" gap={1}>
              <AssessmentIcon color="primary" />
              <Typography variant="subtitle1">Filing Types</Typography>
              {localFilters.filing_types?.length && (
                <Chip
                  label={localFilters.filing_types.length}
                  size="small"
                  color="primary"
                />
              )}
            </Box>
          </AccordionSummary>
          <AccordionDetails>
            <Grid container spacing={1}>
              {filingTypes.map((type) => (
                <Grid item xs={12} sm={6} key={type.value}>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={localFilters.filing_types?.includes(type.value) || false}
                        onChange={(e) => {
                          const currentTypes = localFilters.filing_types || [];
                          const newTypes = e.target.checked
                            ? [...currentTypes, type.value]
                            : currentTypes.filter(t => t !== type.value);
                          handleFilterChange('filing_types', newTypes.length ? newTypes : undefined);
                        }}
                        size="small"
                      />
                    }
                    label={
                      <Box>
                        <Typography variant="body2">{type.value}</Typography>
                        <Typography variant="caption" color="text.secondary">
                          {type.label.split('(')[1]?.replace(')', '') || ''}
                        </Typography>
                      </Box>
                    }
                  />
                </Grid>
              ))}
            </Grid>
          </AccordionDetails>
        </Accordion>

        {/* Date Range Filter */}
        <Accordion
          expanded={expandedSections.includes('dates')}
          onChange={() => handleSectionToggle('dates')}
        >
          <AccordionSummary expandIcon={<ExpandMoreIcon />}>
            <Box display="flex" alignItems="center" gap={1}>
              <DateRangeIcon color="primary" />
              <Typography variant="subtitle1">Date Range</Typography>
              {(localFilters.date_range?.start_date || localFilters.date_range?.end_date) && (
                <Chip label="Active" size="small" color="primary" />
              )}
            </Box>
          </AccordionSummary>
          <AccordionDetails>
            <Grid container spacing={2}>
              <Grid item xs={12} sm={6}>
                <DatePicker
                  label="Start Date"
                  value={localFilters.date_range?.start_date ? new Date(localFilters.date_range.start_date) : null}
                  onChange={(date) => handleDateRangeChange('start_date', date)}
                  slotProps={{
                    textField: {
                      size: "small",
                      fullWidth: true
                    }
                  }}
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <DatePicker
                  label="End Date"
                  value={localFilters.date_range?.end_date ? new Date(localFilters.date_range.end_date) : null}
                  onChange={(date) => handleDateRangeChange('end_date', date)}
                  slotProps={{
                    textField: {
                      size: "small",
                      fullWidth: true
                    }
                  }}
                />
              </Grid>
            </Grid>
          </AccordionDetails>
        </Accordion>

        {/* Advanced Options */}
        <Accordion
          expanded={expandedSections.includes('advanced')}
          onChange={() => handleSectionToggle('advanced')}
        >
          <AccordionSummary expandIcon={<ExpandMoreIcon />}>
            <Box display="flex" alignItems="center" gap={1}>
              <TuneIcon color="primary" />
              <Typography variant="subtitle1">Advanced Options</Typography>
            </Box>
          </AccordionSummary>
          <AccordionDetails>
            <Box display="flex" flexDirection="column" gap={3}>
              {/* Financial Data Filter */}
              <FormControlLabel
                control={
                  <Switch
                    checked={localFilters.has_financial_data === true}
                    onChange={(e) =>
                      handleFilterChange('has_financial_data', e.target.checked ? true : undefined)
                    }
                  />
                }
                label="Only show filings with financial data"
              />

              {/* Minimum Confidence */}
              <FormControl fullWidth size="small">
                <InputLabel>Minimum Confidence Level</InputLabel>
                <Select
                  value={localFilters.min_confidence || ''}
                  onChange={(e) =>
                    handleFilterChange('min_confidence', e.target.value || undefined)
                  }
                >
                  <MenuItem value="">Any Confidence</MenuItem>
                  {confidenceLevels.map((level) => (
                    <MenuItem key={level.value} value={level.value}>
                      {level.label}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Box>
          </AccordionDetails>
        </Accordion>

        {/* Active Filters Summary */}
        {hasActiveFilters && (
          <Box mt={3} p={2} bgcolor="primary.light" borderRadius={1}>
            <Box display="flex" justifyContent="space-between" alignItems="center" mb={1}>
              <Typography variant="subtitle2">Active Filters</Typography>
              <Button
                size="small"
                onClick={handleClearFilters}
                startIcon={<ClearIcon />}
              >
                Clear All
              </Button>
            </Box>
            
            <Box display="flex" flexWrap="wrap" gap={1}>
              {localFilters.companies?.map(ticker => (
                <Chip
                  key={ticker}
                  label={`Company: ${ticker}`}
                  size="small"
                  onDelete={() => {
                    const newCompanies = localFilters.companies?.filter(c => c !== ticker);
                    handleFilterChange('companies', newCompanies?.length ? newCompanies : undefined);
                  }}
                />
              ))}
              
              {localFilters.filing_types?.map(type => (
                <Chip
                  key={type}
                  label={`Filing: ${type}`}
                  size="small"
                  onDelete={() => {
                    const newTypes = localFilters.filing_types?.filter(t => t !== type);
                    handleFilterChange('filing_types', newTypes?.length ? newTypes : undefined);
                  }}
                />
              ))}
              
              {localFilters.date_range?.start_date && (
                <Chip
                  label={`From: ${localFilters.date_range.start_date}`}
                  size="small"
                  onDelete={() => handleDateRangeChange('start_date', null)}
                />
              )}
              
              {localFilters.date_range?.end_date && (
                <Chip
                  label={`To: ${localFilters.date_range.end_date}`}
                  size="small"
                  onDelete={() => handleDateRangeChange('end_date', null)}
                />
              )}
            </Box>
          </Box>
        )}
      </Box>
    </LocalizationProvider>
  );

  // Mobile drawer version
  if (mobile || isMobile) {
    return (
      <Drawer
        anchor="right"
        open={true}
        onClose={onClose}
        PaperProps={{
          sx: { width: isSmallScreen ? '100%' : 400 }
        }}
      >
        <AppBar position="static" elevation={0}>
          <Toolbar>
            <FilterIcon sx={{ mr: 2 }} />
            <Typography variant="h6" sx={{ flexGrow: 1 }}>
              Advanced Filters
            </Typography>
            <IconButton color="inherit" onClick={onClose}>
              <CloseIcon />
            </IconButton>
          </Toolbar>
        </AppBar>
        
        <FilterContent />
      </Drawer>
    );
  }

  // Desktop version
  return (
    <Paper elevation={2} sx={{ mb: 3 }}>
      <Box p={2}>
        <Box display="flex" alignItems="center" justifyContent="space-between" mb={2}>
          <Box display="flex" alignItems="center" gap={1}>
            <FilterIcon color="primary" />
            <Typography variant="h6">Advanced Filters</Typography>
            {hasActiveFilters && (
              <Chip
                label={`${Object.keys(localFilters).length} active`}
                size="small"
                color="primary"
                variant="outlined"
              />
            )}
          </Box>
          
          {hasActiveFilters && (
            <Button
              size="small"
              onClick={handleClearFilters}
              startIcon={<ClearIcon />}
            >
              Clear All
            </Button>
          )}
        </Box>
        
        <FilterContent />
      </Box>
    </Paper>
  );
};

export default AdvancedFilters;
