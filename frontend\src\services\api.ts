/**
 * API Service Layer
 * Phase 6: Frontend Development
 */

import axios, { AxiosResponse } from 'axios';
import {
  QueryRequest,
  QueryResponse,
  Company,
  CompanyListResponse,
  FilingListResponse,
  HealthResponse,
  BatchQueryRequest,
  BatchResponse,
  QueryHistoryEntry,
  HistoryStats,
  ExportRequest,
  ExportResponse,
  APIError,
} from '../types/api';

// API Configuration
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://127.0.0.1:8000/api/v1';

// Create axios instance with default config
const apiClient = axios.create({
  baseURL: API_BASE_URL,
  timeout: 120000, // 2 minutes for complex queries
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor for logging
apiClient.interceptors.request.use(
  (config) => {
    console.log(`API Request: ${config.method?.toUpperCase()} ${config.url}`);
    return config;
  },
  (error) => {
    console.error('API Request Error:', error);
    return Promise.reject(error);
  }
);

// Response interceptor for error handling
apiClient.interceptors.response.use(
  (response) => {
    console.log(`API Response: ${response.status} ${response.config.url}`);
    return response;
  },
  (error) => {
    console.error('API Response Error:', error.response?.data || error.message);
    
    // Transform error to consistent format
    const apiError: APIError = {
      detail: error.response?.data?.detail || error.message || 'An unexpected error occurred',
      status_code: error.response?.status,
      timestamp: error.response?.data?.timestamp,
      errors: error.response?.data?.errors,
      message: error.response?.data?.message,
    };
    
    return Promise.reject(apiError);
  }
);

// Query API
export const queryAPI = {
  // Submit a query
  submitQuery: async (request: QueryRequest): Promise<QueryResponse> => {
    const response: AxiosResponse<QueryResponse> = await apiClient.post('/query/', request);
    return response.data;
  },

  // Get query engine status
  getStatus: async (): Promise<any> => {
    const response = await apiClient.get('/query/status');
    return response.data;
  },

  // Get available models
  getModels: async (): Promise<any> => {
    const response = await apiClient.get('/query/models');
    return response.data;
  },

  // Get supported companies
  getSupportedCompanies: async (): Promise<string[]> => {
    const response = await apiClient.get('/query/companies');
    return response.data.companies || [];
  },
};

// Companies API
export const companiesAPI = {
  // Get companies list with pagination and filters
  getCompanies: async (params?: {
    search?: string;
    sector?: string;
    has_qa_support?: boolean;
    min_filings?: number;
    page?: number;
    page_size?: number;
  }): Promise<CompanyListResponse> => {
    const response: AxiosResponse<CompanyListResponse> = await apiClient.get('/companies/', {
      params,
    });
    return response.data;
  },

  // Get individual company details
  getCompany: async (ticker: string): Promise<Company> => {
    const response: AxiosResponse<Company> = await apiClient.get(`/companies/${ticker}`);
    return response.data;
  },

  // Search companies
  searchCompanies: async (searchRequest: {
    query?: string;
    sector?: string;
    has_qa_support?: boolean;
    min_filings?: number;
    filing_types?: string[];
  }): Promise<CompanyListResponse> => {
    const response: AxiosResponse<CompanyListResponse> = await apiClient.post(
      '/companies/search',
      searchRequest
    );
    return response.data;
  },

  // Get company filings summary
  getCompanyFilings: async (ticker: string): Promise<any> => {
    const response = await apiClient.get(`/companies/${ticker}/filings`);
    return response.data;
  },
};

// Filings API
export const filingsAPI = {
  // Get filings list with filters
  getFilings: async (params?: {
    ticker?: string;
    filing_type?: string;
    start_date?: string;
    end_date?: string;
    processed_only?: boolean;
    has_financial_data?: boolean;
    page?: number;
    page_size?: number;
  }): Promise<FilingListResponse> => {
    const response: AxiosResponse<FilingListResponse> = await apiClient.get('/filings/', {
      params,
    });
    return response.data;
  },

  // Get company-specific filings
  getCompanyFilings: async (ticker: string, filingType: string, limit?: number): Promise<any> => {
    const response = await apiClient.get(`/filings/${ticker}/${filingType}`, {
      params: { limit },
    });
    return response.data;
  },
};

// Health API
export const healthAPI = {
  // Basic health check
  getHealth: async (): Promise<HealthResponse> => {
    const response: AxiosResponse<HealthResponse> = await apiClient.get('/health/');
    return response.data;
  },

  // Detailed health check
  getDetailedHealth: async (): Promise<HealthResponse> => {
    const response: AxiosResponse<HealthResponse> = await apiClient.get('/health/detailed');
    return response.data;
  },
};

// Batch Processing API
export const batchAPI = {
  // Submit batch queries
  submitBatch: async (request: BatchQueryRequest): Promise<BatchResponse> => {
    const response: AxiosResponse<BatchResponse> = await apiClient.post('/batch/queries', request);
    return response.data;
  },

  // Get batch status
  getBatchStatus: async (batchId: string): Promise<BatchResponse> => {
    const response: AxiosResponse<BatchResponse> = await apiClient.get(`/batch/queries/${batchId}`);
    return response.data;
  },

  // List batches
  listBatches: async (limit?: number, offset?: number): Promise<any> => {
    const response = await apiClient.get('/batch/queries', {
      params: { limit, offset },
    });
    return response.data;
  },

  // Upload batch file
  uploadBatchFile: async (file: File): Promise<any> => {
    const formData = new FormData();
    formData.append('file', file);

    const response = await apiClient.post('/batch/upload', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data;
  },

  // Delete batch
  deleteBatch: async (batchId: string): Promise<void> => {
    await apiClient.delete(`/batch/queries/${batchId}`);
  },
};

// Admin API
export const adminAPI = {
  // Cache management
  getCacheStats: async (): Promise<any> => {
    const response = await apiClient.get('/admin/cache/stats');
    return response.data;
  },

  clearCache: async (): Promise<any> => {
    const response = await apiClient.post('/admin/cache/clear');
    return response.data;
  },

  cleanupExpiredCache: async (): Promise<any> => {
    const response = await apiClient.post('/admin/cache/cleanup');
    return response.data;
  },

  // History management
  getHistoryStats: async (days?: number, userId?: string): Promise<HistoryStats> => {
    const response: AxiosResponse<HistoryStats> = await apiClient.get('/admin/history/stats', {
      params: { days, user_id: userId },
    });
    return response.data;
  },

  getQueryHistory: async (params?: {
    user_id?: string;
    session_id?: string;
    limit?: number;
    offset?: number;
  }): Promise<{ history: QueryHistoryEntry[]; count: number }> => {
    const response = await apiClient.get('/admin/history', { params });
    return response.data;
  },

  getQueryById: async (queryId: string): Promise<QueryHistoryEntry> => {
    const response: AxiosResponse<QueryHistoryEntry> = await apiClient.get(
      `/admin/history/${queryId}`
    );
    return response.data;
  },

  exportHistory: async (params?: {
    format?: string;
    user_id?: string;
    start_date?: string;
    end_date?: string;
  }): Promise<any> => {
    const response = await apiClient.post('/admin/history/export', null, { params });
    return response.data;
  },

  // System status
  getSystemStatus: async (): Promise<any> => {
    const response = await apiClient.get('/admin/system/status');
    return response.data;
  },

  runMaintenance: async (): Promise<any> => {
    const response = await apiClient.post('/admin/system/maintenance');
    return response.data;
  },
};

// Export API
export const exportAPI = {
  // Request data export
  requestExport: async (request: ExportRequest): Promise<ExportResponse> => {
    const response: AxiosResponse<ExportResponse> = await apiClient.post('/batch/export', request);
    return response.data;
  },
};

// Utility functions
export const apiUtils = {
  // Check if API is available
  checkConnection: async (): Promise<boolean> => {
    try {
      await healthAPI.getHealth();
      return true;
    } catch {
      return false;
    }
  },

  // Get API base URL
  getBaseURL: (): string => API_BASE_URL,

  // Format error message
  formatError: (error: APIError): string => {
    if (error.errors && error.errors.length > 0) {
      return `${error.detail}: ${error.errors.map((e: any) => e.msg || e).join(', ')}`;
    }
    return error.message || error.detail || 'An unexpected error occurred';
  },
};

// Export all APIs
export default {
  query: queryAPI,
  companies: companiesAPI,
  filings: filingsAPI,
  health: healthAPI,
  batch: batchAPI,
  admin: adminAPI,
  export: exportAPI,
  utils: apiUtils,
};
