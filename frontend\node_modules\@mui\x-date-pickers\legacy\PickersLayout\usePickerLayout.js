import _extends from "@babel/runtime/helpers/esm/extends";
import * as React from 'react';
import { useSlotProps } from '@mui/base/utils';
import { unstable_composeClasses as composeClasses } from '@mui/utils';
import { PickersActionBar } from '../PickersActionBar';
import { getPickersLayoutUtilityClass } from './pickersLayoutClasses';
import { PickersShortcuts } from '../PickersShortcuts';
import { uncapitalizeObjectKeys } from '../internals/utils/slots-migration';
import { jsx as _jsx } from "react/jsx-runtime";
function toolbarHasView(toolbarProps) {
  return toolbarProps.view !== null;
}
var useUtilityClasses = function useUtilityClasses(ownerState) {
  var classes = ownerState.classes,
    isLandscape = ownerState.isLandscape;
  var slots = {
    root: ['root', isLandscape && 'landscape'],
    contentWrapper: ['contentWrapper'],
    toolbar: ['toolbar'],
    actionBar: ['actionBar'],
    tabs: ['tabs'],
    landscape: ['landscape'],
    shortcuts: ['shortcuts']
  };
  return composeClasses(slots, getPickersLayoutUtilityClass, classes);
};
var usePickerLayout = function usePickerLayout(props) {
  var _slots$actionBar, _slots$shortcuts;
  var _ref = props,
    wrapperVariant = _ref.wrapperVariant,
    onAccept = _ref.onAccept,
    onClear = _ref.onClear,
    onCancel = _ref.onCancel,
    onSetToday = _ref.onSetToday,
    view = _ref.view,
    views = _ref.views,
    onViewChange = _ref.onViewChange,
    value = _ref.value,
    onChange = _ref.onChange,
    onSelectShortcut = _ref.onSelectShortcut,
    isValid = _ref.isValid,
    isLandscape = _ref.isLandscape,
    disabled = _ref.disabled,
    readOnly = _ref.readOnly,
    children = _ref.children,
    components = _ref.components,
    componentsProps = _ref.componentsProps,
    innerSlots = _ref.slots,
    innerSlotProps = _ref.slotProps;
  var slots = innerSlots != null ? innerSlots : uncapitalizeObjectKeys(components);
  var slotProps = innerSlotProps != null ? innerSlotProps : componentsProps;
  var classes = useUtilityClasses(props);

  // Action bar

  var ActionBar = (_slots$actionBar = slots == null ? void 0 : slots.actionBar) != null ? _slots$actionBar : PickersActionBar;
  var actionBarProps = useSlotProps({
    elementType: ActionBar,
    externalSlotProps: slotProps == null ? void 0 : slotProps.actionBar,
    additionalProps: {
      onAccept: onAccept,
      onClear: onClear,
      onCancel: onCancel,
      onSetToday: onSetToday,
      actions: wrapperVariant === 'desktop' ? [] : ['cancel', 'accept'],
      className: classes.actionBar
    },
    ownerState: _extends({}, props, {
      wrapperVariant: wrapperVariant
    })
  });
  var actionBar = /*#__PURE__*/_jsx(ActionBar, _extends({}, actionBarProps));

  // Toolbar

  var Toolbar = slots == null ? void 0 : slots.toolbar;
  var toolbarProps = useSlotProps({
    elementType: Toolbar,
    externalSlotProps: slotProps == null ? void 0 : slotProps.toolbar,
    additionalProps: {
      isLandscape: isLandscape,
      onChange: onChange,
      value: value,
      view: view,
      onViewChange: onViewChange,
      views: views,
      disabled: disabled,
      readOnly: readOnly,
      className: classes.toolbar
    },
    ownerState: _extends({}, props, {
      wrapperVariant: wrapperVariant
    })
  });
  var toolbar = toolbarHasView(toolbarProps) && !!Toolbar ? /*#__PURE__*/_jsx(Toolbar, _extends({}, toolbarProps)) : null;

  // Content

  var content = children;

  // Tabs

  var Tabs = slots == null ? void 0 : slots.tabs;
  var tabs = view && Tabs ? /*#__PURE__*/_jsx(Tabs, _extends({
    view: view,
    onViewChange: onViewChange,
    className: classes.tabs
  }, slotProps == null ? void 0 : slotProps.tabs)) : null;

  // Shortcuts

  var Shortcuts = (_slots$shortcuts = slots == null ? void 0 : slots.shortcuts) != null ? _slots$shortcuts : PickersShortcuts;
  var shortcutsProps = useSlotProps({
    elementType: Shortcuts,
    externalSlotProps: slotProps == null ? void 0 : slotProps.shortcuts,
    additionalProps: {
      isValid: isValid,
      isLandscape: isLandscape,
      onChange: onSelectShortcut,
      className: classes.shortcuts
    },
    ownerState: {
      isValid: isValid,
      isLandscape: isLandscape,
      onChange: onSelectShortcut,
      className: classes.shortcuts,
      wrapperVariant: wrapperVariant
    }
  });
  var shortcuts = view && !!Shortcuts ? /*#__PURE__*/_jsx(Shortcuts, _extends({}, shortcutsProps)) : null;
  return {
    toolbar: toolbar,
    content: content,
    tabs: tabs,
    actionBar: actionBar,
    shortcuts: shortcuts
  };
};
export default usePickerLayout;