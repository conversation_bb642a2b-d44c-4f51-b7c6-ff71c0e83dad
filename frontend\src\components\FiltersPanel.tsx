/**
 * Filters Panel Component
 * Phase 6: Frontend Development - Task 6.1
 */

import React, { useState, useEffect } from 'react';
import {
  Box,
  Paper,
  Typography,
  Chip,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  OutlinedInput,
  Checkbox,
  ListItemText,
  TextField,
  Collapse,
  IconButton,
  Tooltip,
  Autocomplete,
  FormControlLabel,
  Switch,
  Divider,
} from '@mui/material';
import {
  FilterList as FilterIcon,
  Clear as ClearIcon,
  ExpandMore as ExpandMoreIcon,
  ExpandLess as ExpandLessIcon,
  Business as BusinessIcon,
} from '@mui/icons-material';
import { Company, QueryFilters } from '../types/api';

interface FiltersPanelProps {
  companies: Company[];
  filters: QueryFilters;
  onFiltersChange: (filters: QueryFilters) => void;
  isLoading?: boolean;
  compact?: boolean;
}

const FILING_TYPES = [
  { value: '10-K', label: '10-K (Annual Report)' },
  { value: '10-Q', label: '10-Q (Quarterly Report)' },
  { value: '8-K', label: '8-K (Current Report)' },
  { value: 'DEF 14A', label: 'DEF 14A (Proxy Statement)' },
];

const CONFIDENCE_LEVELS = [
  { value: 'high', label: 'High Confidence' },
  { value: 'medium', label: 'Medium Confidence' },
  { value: 'low', label: 'Low Confidence' },
];

const FiltersPanel: React.FC<FiltersPanelProps> = ({
  companies,
  filters,
  onFiltersChange,
  isLoading = false,
  compact = false,
}) => {
  const [expanded, setExpanded] = useState(!compact);
  const [localFilters, setLocalFilters] = useState<QueryFilters>(filters);

  // Update local filters when props change
  useEffect(() => {
    setLocalFilters(filters);
  }, [filters]);

  // Handle filter changes
  const handleFilterChange = (key: keyof QueryFilters, value: any) => {
    const newFilters = { ...localFilters, [key]: value };
    setLocalFilters(newFilters);
    onFiltersChange(newFilters);
  };

  // Handle date range changes
  const handleDateRangeChange = (field: 'start_date' | 'end_date', value: string) => {
    const newDateRange = {
      ...localFilters.date_range,
      [field]: value || undefined,
    };
    handleFilterChange('date_range', newDateRange);
  };

  // Clear all filters
  const handleClearFilters = () => {
    const clearedFilters: QueryFilters = {};
    setLocalFilters(clearedFilters);
    onFiltersChange(clearedFilters);
  };

  // Check if any filters are active
  const hasActiveFilters = Boolean(
    localFilters.companies?.length ||
    localFilters.filing_types?.length ||
    localFilters.date_range?.start_date ||
    localFilters.date_range?.end_date ||
    localFilters.has_financial_data !== undefined ||
    localFilters.min_confidence
  );

  // Get supported companies for autocomplete
  const supportedCompanies = companies.filter(c => c.supported_for_qa);

  return (
    <Paper elevation={2} sx={{ mb: 3 }}>
      {/* Header */}
      <Box
        display="flex"
        alignItems="center"
        justifyContent="space-between"
        p={2}
        sx={{ cursor: compact ? 'pointer' : 'default' }}
        onClick={compact ? () => setExpanded(!expanded) : undefined}
      >
        <Box display="flex" alignItems="center" gap={1}>
          <FilterIcon color="primary" />
          <Typography variant="h6">
            Filters
          </Typography>
          {hasActiveFilters && (
            <Chip
              label={`${Object.keys(localFilters).length} active`}
              size="small"
              color="primary"
              variant="outlined"
            />
          )}
        </Box>

        <Box display="flex" alignItems="center" gap={1}>
          {hasActiveFilters && (
            <Tooltip title="Clear all filters">
              <IconButton
                size="small"
                onClick={(e) => {
                  e.stopPropagation();
                  handleClearFilters();
                }}
                disabled={isLoading}
              >
                <ClearIcon />
              </IconButton>
            </Tooltip>
          )}
          
          {compact && (
            <IconButton size="small">
              {expanded ? <ExpandLessIcon /> : <ExpandMoreIcon />}
            </IconButton>
          )}
        </Box>
      </Box>

      {/* Filters Content */}
      <Collapse in={expanded}>
        <Box p={2} pt={0}>
          <Divider sx={{ mb: 3 }} />

          <Box display="flex" flexDirection="column" gap={3}>
            {/* Company Selection */}
            <Box>
              <Box display="flex" alignItems="center" gap={1} mb={1}>
                <BusinessIcon fontSize="small" />
                <Typography variant="subtitle2">
                  Companies
                </Typography>
              </Box>
              <Autocomplete
                multiple
                options={supportedCompanies}
                getOptionLabel={(option) => `${option.ticker} - ${option.name}`}
                value={supportedCompanies.filter(c => 
                  localFilters.companies?.includes(c.ticker)
                ) || []}
                onChange={(_, newValue) => {
                  handleFilterChange('companies', newValue.map(c => c.ticker));
                }}
                disabled={isLoading}
                renderInput={(params) => (
                  <TextField
                    {...params}
                    placeholder="Select companies..."
                    size="small"
                  />
                )}
                renderTags={(value, getTagProps) =>
                  value.map((option, index) => (
                    <Chip
                      {...getTagProps({ index })}
                      key={option.ticker}
                      label={option.ticker}
                      size="small"
                      color="primary"
                      variant="outlined"
                    />
                  ))
                }
                renderOption={(props, option) => (
                  <Box component="li" {...props}>
                    <Box>
                      <Typography variant="body2">
                        <strong>{option.ticker}</strong> - {option.name}
                      </Typography>
                      <Typography variant="caption" color="text.secondary">
                        {option.sector} • {option.stats.total_filings} filings
                      </Typography>
                    </Box>
                  </Box>
                )}
              />
            </Box>

            {/* Filing Types */}
            <Box>
              <Typography variant="subtitle2" gutterBottom>
                Filing Types
              </Typography>
              <FormControl fullWidth size="small">
                <InputLabel>Select filing types</InputLabel>
                <Select
                  multiple
                  value={localFilters.filing_types || []}
                  onChange={(e) => handleFilterChange('filing_types', e.target.value)}
                  input={<OutlinedInput label="Select filing types" />}
                  disabled={isLoading}
                  renderValue={(selected) => (
                    <Box display="flex" flexWrap="wrap" gap={0.5}>
                      {(selected as string[]).map((value) => (
                        <Chip
                          key={value}
                          label={value}
                          size="small"
                          color="secondary"
                          variant="outlined"
                        />
                      ))}
                    </Box>
                  )}
                >
                  {FILING_TYPES.map((type) => (
                    <MenuItem key={type.value} value={type.value}>
                      <Checkbox 
                        checked={(localFilters.filing_types || []).includes(type.value)}
                        size="small"
                      />
                      <ListItemText primary={type.label} />
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Box>

            {/* Date Range */}
            <Box>
              <Typography variant="subtitle2" gutterBottom>
                Date Range
              </Typography>
              <Box display="flex" gap={2}>
                <TextField
                  label="Start Date"
                  type="date"
                  size="small"
                  value={localFilters.date_range?.start_date || ''}
                  onChange={(e) => handleDateRangeChange('start_date', e.target.value)}
                  disabled={isLoading}
                  InputLabelProps={{ shrink: true }}
                  sx={{ flexGrow: 1 }}
                />
                <TextField
                  label="End Date"
                  type="date"
                  size="small"
                  value={localFilters.date_range?.end_date || ''}
                  onChange={(e) => handleDateRangeChange('end_date', e.target.value)}
                  disabled={isLoading}
                  InputLabelProps={{ shrink: true }}
                  sx={{ flexGrow: 1 }}
                />
              </Box>
            </Box>

            {/* Additional Filters */}
            <Box>
              <Typography variant="subtitle2" gutterBottom>
                Content Filters
              </Typography>
              
              <Box display="flex" flexDirection="column" gap={2}>
                {/* Financial Data Filter */}
                <FormControlLabel
                  control={
                    <Switch
                      checked={localFilters.has_financial_data === true}
                      onChange={(e) => 
                        handleFilterChange('has_financial_data', e.target.checked ? true : undefined)
                      }
                      disabled={isLoading}
                      size="small"
                    />
                  }
                  label="Only show filings with financial data"
                />

                {/* Minimum Confidence */}
                <FormControl size="small" sx={{ minWidth: 200 }}>
                  <InputLabel>Minimum Confidence</InputLabel>
                  <Select
                    value={localFilters.min_confidence || ''}
                    label="Minimum Confidence"
                    onChange={(e) => 
                      handleFilterChange('min_confidence', e.target.value || undefined)
                    }
                    disabled={isLoading}
                  >
                    <MenuItem value="">Any Confidence</MenuItem>
                    {CONFIDENCE_LEVELS.map((level) => (
                      <MenuItem key={level.value} value={level.value}>
                        {level.label}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Box>
            </Box>
          </Box>

          {/* Filter Summary */}
          {hasActiveFilters && (
            <Box mt={3} p={2} bgcolor="primary.light" borderRadius={1}>
              <Typography variant="caption" display="block" gutterBottom>
                <strong>Active Filters:</strong>
              </Typography>
              <Box display="flex" flexWrap="wrap" gap={1}>
                {localFilters.companies?.map(ticker => (
                  <Chip
                    key={ticker}
                    label={`Company: ${ticker}`}
                    size="small"
                    onDelete={() => {
                      const newCompanies = localFilters.companies?.filter(c => c !== ticker);
                      handleFilterChange('companies', newCompanies?.length ? newCompanies : undefined);
                    }}
                  />
                ))}
                
                {localFilters.filing_types?.map(type => (
                  <Chip
                    key={type}
                    label={`Filing: ${type}`}
                    size="small"
                    onDelete={() => {
                      const newTypes = localFilters.filing_types?.filter(t => t !== type);
                      handleFilterChange('filing_types', newTypes?.length ? newTypes : undefined);
                    }}
                  />
                ))}
                
                {localFilters.date_range?.start_date && (
                  <Chip
                    label={`From: ${localFilters.date_range.start_date}`}
                    size="small"
                    onDelete={() => handleDateRangeChange('start_date', '')}
                  />
                )}
                
                {localFilters.date_range?.end_date && (
                  <Chip
                    label={`To: ${localFilters.date_range.end_date}`}
                    size="small"
                    onDelete={() => handleDateRangeChange('end_date', '')}
                  />
                )}
                
                {localFilters.has_financial_data && (
                  <Chip
                    label="Has Financial Data"
                    size="small"
                    onDelete={() => handleFilterChange('has_financial_data', undefined)}
                  />
                )}
                
                {localFilters.min_confidence && (
                  <Chip
                    label={`Min Confidence: ${localFilters.min_confidence}`}
                    size="small"
                    onDelete={() => handleFilterChange('min_confidence', undefined)}
                  />
                )}
              </Box>
            </Box>
          )}
        </Box>
      </Collapse>
    </Paper>
  );
};

export default FiltersPanel;
