import * as React from 'react';
import { UseMobilePickerParams, UseMobilePickerProps } from './useMobilePicker.types';
import { DateOrTimeViewWithMeridiem } from '../../models';
/**
 * Hook managing all the single-date mobile pickers:
 * - MobileDatePicker
 * - MobileDateTimePicker
 * - MobileTimePicker
 */
export declare const useMobilePicker: <TDate, TView extends DateOrTimeViewWithMeridiem, TExternalProps extends UseMobilePickerProps<TDate, TView, any, TExternalProps>>({ props, getOpenDialogAriaText, ...pickerParams }: UseMobilePickerParams<TDate, TView, TExternalProps>) => {
    renderPicker: () => React.JSX.Element;
};
