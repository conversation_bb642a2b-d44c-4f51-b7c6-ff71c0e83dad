import * as React from 'react';
import { validateDate } from '../internals/utils/validation/validateDate';
import { useLocalizationContext } from '../internals/hooks/useUtils';
export var useIsDateDisabled = function useIsDateDisabled(_ref) {
  var shouldDisableDate = _ref.shouldDisableDate,
    shouldDisableMonth = _ref.shouldDisableMonth,
    shouldDisableYear = _ref.shouldDisableYear,
    minDate = _ref.minDate,
    maxDate = _ref.maxDate,
    disableFuture = _ref.disableFuture,
    disablePast = _ref.disablePast,
    timezone = _ref.timezone;
  var adapter = useLocalizationContext();
  return React.useCallback(function (day) {
    return validateDate({
      adapter: adapter,
      value: day,
      props: {
        shouldDisableDate: shouldDisableDate,
        shouldDisableMonth: shouldDisableMonth,
        shouldDisableYear: shouldDisableYear,
        minDate: minDate,
        maxDate: maxDate,
        disableFuture: disableFuture,
        disablePast: disablePast,
        timezone: timezone
      }
    }) !== null;
  }, [adapter, shouldDisableDate, shouldDisableMonth, shouldDisableYear, minDate, maxDate, disableFuture, disablePast, timezone]);
};