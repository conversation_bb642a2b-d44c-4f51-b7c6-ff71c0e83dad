import * as React from 'react';
import { DefaultizedProps } from '../internals/models/helpers';
import { TimeClockSlotsComponent, TimeClockSlotsComponentsProps } from '../TimeClock/TimeClock.types';
import { BasePickerInputProps } from '../internals/models/props/basePickerProps';
import { BaseTimeValidationProps } from '../internals/models/validation';
import { LocalizedComponent } from '../locales/utils/pickersLocaleTextApi';
import { TimePickerToolbarProps, ExportedTimePickerToolbarProps } from './TimePickerToolbar';
import { TimeValidationError } from '../models';
import { PickerViewRendererLookup } from '../internals/hooks/usePicker/usePickerViews';
import { TimeViewRendererProps } from '../timeViewRenderers';
import { UncapitalizeObjectKeys } from '../internals/utils/slots-migration';
import { BaseClockProps, ExportedBaseClockProps } from '../internals/models/props/clock';
import { TimeViewWithMeridiem } from '../internals/models';
export interface BaseTimePickerSlotsComponent<TDate> extends TimeClockSlotsComponent {
    /**
     * Custom component for the toolbar rendered above the views.
     * @default TimePickerToolbar
     */
    Toolbar?: React.JSXElementConstructor<TimePickerToolbarProps<TDate>>;
}
export interface BaseTimePickerSlotsComponentsProps extends TimeClockSlotsComponentsProps {
    toolbar?: ExportedTimePickerToolbarProps;
}
export interface BaseTimePickerProps<TDate, TView extends TimeViewWithMeridiem> extends BasePickerInputProps<TDate | null, TDate, TView, TimeValidationError>, ExportedBaseClockProps<TDate> {
    /**
     * Display ampm controls under the clock (instead of in the toolbar).
     * @default true on desktop, false on mobile
     */
    ampmInClock?: boolean;
    /**
     * Overridable components.
     * @default {}
     * @deprecated Please use `slots`.
     */
    components?: BaseTimePickerSlotsComponent<TDate>;
    /**
     * The props used for each component slot.
     * @default {}
     * @deprecated Please use `slotProps`.
     */
    componentsProps?: BaseTimePickerSlotsComponentsProps;
    /**
     * Overridable component slots.
     * @default {}
     */
    slots?: UncapitalizeObjectKeys<BaseTimePickerSlotsComponent<TDate>>;
    /**
     * The props used for each component slot.
     * @default {}
     */
    slotProps?: BaseTimePickerSlotsComponentsProps;
    /**
     * Define custom view renderers for each section.
     * If `null`, the section will only have field editing.
     * If `undefined`, internally defined view will be the used.
     */
    viewRenderers?: Partial<PickerViewRendererLookup<TDate | null, TView, TimeViewRendererProps<TView, BaseClockProps<TDate, TView>>, {}>>;
}
type UseTimePickerDefaultizedProps<TDate, TView extends TimeViewWithMeridiem, Props extends BaseTimePickerProps<TDate, TView>> = LocalizedComponent<TDate, Omit<DefaultizedProps<Props, 'views' | 'openTo' | 'ampm' | keyof BaseTimeValidationProps>, 'components' | 'componentsProps'>>;
export declare function useTimePickerDefaultizedProps<TDate, TView extends TimeViewWithMeridiem, Props extends BaseTimePickerProps<TDate, TView>>(props: Props, name: string): UseTimePickerDefaultizedProps<TDate, TView, Props>;
export {};
