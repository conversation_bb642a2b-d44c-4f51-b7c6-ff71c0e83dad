#!/usr/bin/env python3
"""
Test Queries with New Sample Data
"""

import requests
import json
import time

def test_query(question, description=""):
    """Test a single query"""
    print(f"\n🔍 Testing: {description or question}")
    print("-" * 50)
    
    try:
        start_time = time.time()
        
        response = requests.post(
            "http://127.0.0.1:8000/api/v1/query/",
            json={
                "question": question,
                "max_chunks": 3,
                "include_sources": True,
                "include_confidence": True
            },
            timeout=30
        )
        
        processing_time = time.time() - start_time
        
        if response.status_code == 200:
            result = response.json()
            
            print(f"✅ SUCCESS ({processing_time:.2f}s)")
            print(f"📊 Success: {result.get('success')}")
            print(f"🤖 Model: {result.get('model_used', 'unknown')}")
            print(f"📚 Sources: {len(result.get('sources', []))}")
            
            answer = result.get('answer', '')
            if answer:
                # Show first 200 characters
                preview = answer[:200] + "..." if len(answer) > 200 else answer
                print(f"💬 Answer: {preview}")
                
                # Show sources
                sources = result.get('sources', [])
                if sources:
                    print(f"📖 Sources:")
                    for i, source in enumerate(sources[:3], 1):
                        ticker = source.get('ticker', 'Unknown')
                        section = source.get('section_name', 'Unknown')
                        score = source.get('score', 0)
                        print(f"   {i}. [{ticker}] {section} (Score: {score:.3f})")
            else:
                print(f"❌ No answer generated")
                
            return result.get('success', False)
        else:
            print(f"❌ HTTP Error: {response.status_code}")
            try:
                error_data = response.json()
                print(f"   Error: {error_data.get('detail', 'Unknown error')}")
            except:
                print(f"   Raw response: {response.text}")
            return False
            
    except requests.exceptions.Timeout:
        print(f"❌ TIMEOUT after 30 seconds")
        return False
    except requests.exceptions.ConnectionError:
        print(f"❌ CONNECTION ERROR - Backend not available")
        return False
    except Exception as e:
        print(f"❌ ERROR: {str(e)}")
        return False

def main():
    """Run all test queries"""
    print("🧪 TESTING QUERIES WITH NEW SAMPLE DATA")
    print("=" * 60)
    
    # Test queries that should work with our sample data
    test_queries = [
        ("What was Apple's revenue in Q3 2023?", "Apple Q3 2023 Revenue"),
        ("How did Apple's Services revenue perform?", "Apple Services Performance"),
        ("What is Apple's business model?", "Apple Business Model"),
        ("What are Apple's main risk factors?", "Apple Risk Factors"),
        ("How did Microsoft perform in Q1 2024?", "Microsoft Q1 2024 Performance"),
        ("Tell me about iPhone sales", "iPhone Sales Information"),
        ("What are the main revenue streams for Apple?", "Apple Revenue Streams")
    ]
    
    successful_tests = 0
    total_tests = len(test_queries)
    
    for question, description in test_queries:
        success = test_query(question, description)
        if success:
            successful_tests += 1
        time.sleep(1)  # Brief pause between tests
    
    print(f"\n📊 TEST SUMMARY")
    print("=" * 30)
    print(f"✅ Successful: {successful_tests}/{total_tests}")
    print(f"❌ Failed: {total_tests - successful_tests}/{total_tests}")
    
    if successful_tests == total_tests:
        print(f"\n🎉 ALL TESTS PASSED! Your SEC Filing QA Agent is working perfectly!")
    elif successful_tests > 0:
        print(f"\n✅ PARTIAL SUCCESS! {successful_tests} out of {total_tests} queries worked.")
    else:
        print(f"\n❌ ALL TESTS FAILED! Check backend status and data loading.")
    
    print(f"\n💡 Try these queries in your frontend at http://localhost:3000")

if __name__ == "__main__":
    main()
