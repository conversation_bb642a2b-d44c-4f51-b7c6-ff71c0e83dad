#!/usr/bin/env python3
"""
Load Sample SEC Filing Data
Loads sample Apple SEC filing data using only local embedding models
"""

import asyncio
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.services.document_vectorizer import document_vectorizer
from app.services.embedding_service import embedding_service
from app.services.vector_storage import vector_storage

async def load_sample_data():
    """Load sample SEC filing data into the vector database"""
    print("🚀 Loading Sample SEC Filing Data")
    print("=" * 50)
    
    # Force fallback mode to use local embeddings only
    embedding_service.fallback_active = True
    embedding_service.openai_client = None
    print("✅ Forced local embedding mode (Sentence Transformer)")
    
    # Sample Apple SEC filing chunks
    sample_chunks = [
        {
            "content": "Apple Inc. reported net sales of $81.8 billion for Q3 2023, compared to $83.0 billion in the prior year quarter, representing a decrease of 1% year-over-year. iPhone revenue was $39.7 billion, Mac revenue was $6.8 billion, iPad revenue was $5.8 billion, and Services revenue was $21.2 billion.",
            "metadata": {
                "ticker": "AAPL",
                "company_name": "Apple Inc.",
                "filing_type": "10-Q",
                "section_name": "Financial Performance",
                "accession_number": "0000320193-23-000064",
                "filing_date": "2023-08-03",
                "quarter": "Q3 2023",
                "content_preview": "Apple Inc. reported net sales of $81.8 billion for Q3 2023...",
                "key_phrases": ["net sales", "revenue", "iPhone", "Services", "year-over-year"]
            }
        },
        {
            "content": "Services revenue increased 8% year-over-year to $21.2 billion, driven by growth across multiple categories including the App Store, advertising, and cloud services. This represents Apple's highest-margin business segment and continues to show strong growth momentum.",
            "metadata": {
                "ticker": "AAPL",
                "company_name": "Apple Inc.",
                "filing_type": "10-Q",
                "section_name": "Services Revenue",
                "accession_number": "0000320193-23-000064",
                "filing_date": "2023-08-03",
                "quarter": "Q3 2023",
                "content_preview": "Services revenue increased 8% year-over-year to $21.2 billion...",
                "key_phrases": ["Services revenue", "App Store", "advertising", "cloud services", "growth"]
            }
        },
        {
            "content": "Apple's business model is built around designing and manufacturing consumer electronics, computer software, and online services. The company's primary revenue streams include iPhone sales, Mac computers, iPad tablets, Apple Watch, AirPods, and a growing Services business including the App Store, iCloud, Apple Music, and Apple TV+.",
            "metadata": {
                "ticker": "AAPL",
                "company_name": "Apple Inc.",
                "filing_type": "10-K",
                "section_name": "Business Overview",
                "accession_number": "0000320193-23-000106",
                "filing_date": "2023-11-03",
                "quarter": "FY 2023",
                "content_preview": "Apple's business model is built around designing and manufacturing...",
                "key_phrases": ["business model", "consumer electronics", "iPhone", "Services", "revenue streams"]
            }
        },
        {
            "content": "The Company's business is subject to various risks including global economic conditions, supply chain disruptions, intense competition in the technology industry, regulatory changes, and cybersecurity threats that could materially affect financial results and operations.",
            "metadata": {
                "ticker": "AAPL",
                "company_name": "Apple Inc.",
                "filing_type": "10-K",
                "section_name": "Risk Factors",
                "accession_number": "0000320193-23-000106",
                "filing_date": "2023-11-03",
                "quarter": "FY 2023",
                "content_preview": "The Company's business is subject to various risks...",
                "key_phrases": ["risk factors", "economic conditions", "supply chain", "competition", "regulatory"]
            }
        },
        {
            "content": "Microsoft Corporation reported revenue of $56.2 billion for Q1 2024, representing 13% growth year-over-year. Productivity and Business Processes revenue was $18.6 billion, More Personal Computing revenue was $13.7 billion, and Intelligent Cloud revenue was $24.3 billion, showing strong performance across all segments.",
            "metadata": {
                "ticker": "MSFT",
                "company_name": "Microsoft Corporation",
                "filing_type": "10-Q",
                "section_name": "Financial Performance",
                "accession_number": "0000789019-23-000057",
                "filing_date": "2023-10-25",
                "quarter": "Q1 2024",
                "content_preview": "Microsoft Corporation reported revenue of $56.2 billion...",
                "key_phrases": ["revenue", "growth", "Productivity", "Intelligent Cloud", "segments"]
            }
        }
    ]
    
    print(f"📊 Processing {len(sample_chunks)} sample chunks...")
    
    # Add each chunk to the vector database
    for i, chunk in enumerate(sample_chunks, 1):
        try:
            print(f"  📝 Adding chunk {i}/{len(sample_chunks)}: {chunk['metadata']['ticker']} - {chunk['metadata']['section_name']}")

            # Generate embedding for the content
            embeddings = await embedding_service.generate_embeddings([chunk["content"]])
            if not embeddings:
                print(f"    ❌ Failed to generate embedding for chunk {i}")
                continue

            # Store in vector database
            vector_ids = await vector_storage.store_vectors(
                vectors=embeddings,
                metadata_list=[chunk["metadata"]]
            )

            print(f"    ✅ Successfully added chunk {i} with ID: {vector_ids[0] if vector_ids else 'unknown'}")

        except Exception as e:
            print(f"    ❌ Failed to add chunk {i}: {str(e)}")
    
    # Get final status
    status = document_vectorizer.get_pipeline_status()
    print(f"\n📈 Vector Database Status:")
    print(f"  📊 Total vectors: {status.get('total_vectors', 0)}")
    print(f"  📏 Vector dimension: {status.get('vector_dimension', 0)}")
    print(f"  🔧 Embedding service: {status.get('embedding_service', 'Unknown')}")
    
    print(f"\n🎉 Sample data loading complete!")
    print(f"💡 You can now test queries like:")
    print(f"   - 'What was Apple's revenue in Q3 2023?'")
    print(f"   - 'How did Apple's Services revenue perform?'")
    print(f"   - 'What is Apple's business model?'")
    print(f"   - 'What are Apple's main risk factors?'")
    print(f"   - 'How did Microsoft perform in Q1 2024?'")

if __name__ == "__main__":
    asyncio.run(load_sample_data())
